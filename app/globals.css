@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 255 249 253; /* <PERSON> White #FFF9FD */
    --foreground: 15 22 50; /* Oxford Blue #0F1632 */
    --card: 255 249 253;
    --card-foreground: 15 22 50;
    --popover: 255 249 253;
    --popover-foreground: 15 22 50;
    --primary: 248 214 19; /* Starlight Yellow #F8D613 */
    --primary-foreground: 15 22 50;
    --secondary: 240 244 248;
    --secondary-foreground: 15 22 50;
    --muted: 240 244 248;
    --muted-foreground: 100 116 139;
    --accent: 0 159 253; /* Carolina Blue #009FFD */
    --accent-foreground: 255 249 253;
    --destructive: 239 68 68;
    --destructive-foreground: 255 249 253;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 248 214 19;
    --radius: 0.75rem;
  }

  .dark {
    --background: 15 22 50;
    --foreground: 255 249 253;
    --card: 15 22 50;
    --card-foreground: 255 249 253;
    --popover: 15 22 50;
    --popover-foreground: 255 249 253;
    --primary: 248 214 19;
    --primary-foreground: 15 22 50;
    --secondary: 30 41 59;
    --secondary-foreground: 255 249 253;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 0 159 253;
    --accent-foreground: 255 249 253;
    --destructive: 220 38 38;
    --destructive-foreground: 255 249 253;
    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 248 214 19;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins), sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Brand Pattern Background */
.brand-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F8D613' fill-opacity='0.1'%3E%3Cpath d='M15 30c0-8.284 6.716-15 15-15s15 6.716 15 15-6.716 15-15 15-15-6.716-15-15zm15-10c-5.523 0-10 4.477-10 10s4.477 10 10 10 10-4.477 10-10-4.477-10-10-10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Title Typography - Bold/Black weight for titles */
.title-font {
  font-family: var(--font-poppins), sans-serif;
  font-weight: 800;
  letter-spacing: -0.025em;
}

/* Body Typography - Medium/Regular for body text */
.body-font {
  font-family: var(--font-poppins), sans-serif;
  font-weight: 400;
}

.lead-font {
  font-family: var(--font-poppins), sans-serif;
  font-weight: 500;
}
