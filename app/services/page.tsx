import Image from "next/image"
import Link from "next/link"
import { Brain, Heart, Home, LifeBuoy, School, Users, Phone, Calendar, MapPin } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"

const services = [
  {
    icon: <LifeBuoy className="h-10 w-10 text-yellow-500" />,
    title: "Emergency Housing",
    description: "Immediate safe accommodation for those in crisis situations with 24/7 support.",
    features: ["24/7 availability", "Crisis intervention", "Immediate placement", "Safety assessment"],
    contactPerson: "Emergency Team",
    phone: "0800 123 4567",
  },
  {
    icon: <Brain className="h-10 w-10 text-yellow-500" />,
    title: "Mental Health & Advocacy",
    description: "Professional support for mental wellbeing and personal advocacy services.",
    features: ["Counseling services", "Therapy sessions", "Advocacy support", "Peer mentoring"],
    contactPerson: "<PERSON>. <PERSON>",
    phone: "0161 234 5678",
  },
  {
    icon: <Heart className="h-10 w-10 text-yellow-500" />,
    title: "Benefits Support",
    description: "Assistance with navigating and accessing available benefits and financial aid.",
    features: ["Benefits assessment", "Application support", "Appeals assistance", "Financial planning"],
    contactPerson: "Michael Chen",
    phone: "0121 345 6789",
  },
  {
    icon: <Home className="h-10 w-10 text-yellow-500" />,
    title: "Resettlement Services",
    description: "Help with finding permanent housing and settling into communities safely.",
    features: ["Housing search", "Tenancy support", "Community integration", "Move-in assistance"],
    contactPerson: "Emma Thompson",
    phone: "0113 456 7890",
  },
  {
    icon: <School className="h-10 w-10 text-yellow-500" />,
    title: "Life Skills & Independence",
    description: "Training and support to build independence and confidence for the future.",
    features: ["Cooking classes", "Budgeting workshops", "Job readiness", "Digital literacy"],
    contactPerson: "James Rodriguez",
    phone: "0207 567 8901",
  },
  {
    icon: <Users className="h-10 w-10 text-yellow-500" />,
    title: "Community Integration",
    description: "Programs to help individuals connect with their local community and support networks.",
    features: ["Social activities", "Volunteer opportunities", "Support groups", "Community events"],
    contactPerson: "Lisa Patel",
    phone: "0141 678 9012",
  },
]

const successStories = [
  {
    name: "Sarah M.",
    age: 28,
    story:
      "After experiencing homelessness, Sarah found not just a home but a community. With mental health support and life skills training, she's now working as a peer mentor helping others.",
    image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",
    services: ["Emergency Housing", "Mental Health Support", "Life Skills Training"],
    outcome: "Now employed as peer mentor",
  },
  {
    name: "Ahmed K.",
    age: 34,
    story:
      "As a refugee, Ahmed needed more than just housing. Our resettlement team helped him navigate benefits, learn English, and find employment in his field.",
    image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg",
    services: ["Resettlement Services", "Benefits Support", "Community Integration"],
    outcome: "Secured permanent housing and employment",
  },
  {
    name: "Maria & Family",
    age: 31,
    story:
      "Fleeing domestic violence with two children, Maria found safety and support. Our family services helped her rebuild her life and create a stable home for her children.",
    image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg",
    services: ["Emergency Housing", "Family Support", "Benefits Support"],
    outcome: "Independent living with children thriving",
  },
]

export default function ServicesPage() {
  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gray-800">
          <div className="absolute inset-0 bg-black/40"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.pexels.com/photos/1157557/pexels-photo-1157557.jpeg"
              alt="Support services community"
              fill
              className="object-cover"
            />
          </div>
          <div className="container relative z-10 text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Support Services</h1>
            <p className="mt-6 mx-auto max-w-2xl text-lg text-white/90">
              Comprehensive support services designed around each person's unique journey to independence and stability.
            </p>
          </div>
        </section>

        {/* Services Grid */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Our Services</h2>
              <p className="mt-4 text-lg text-gray-600">
                Holistic support that addresses every aspect of rebuilding lives
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {services.map((service, index) => (
                <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="mb-4">{service.icon}</div>
                    <CardTitle className="text-xl text-gray-800">{service.title}</CardTitle>
                    <CardDescription className="text-gray-600">{service.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-gray-800 mb-2">What we offer:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {service.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center">
                              <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2"></div>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="pt-4 border-t border-gray-100">
                        <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                          <Users className="h-4 w-4" />
                          <span className="font-medium">{service.contactPerson}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Phone className="h-4 w-4" />
                          <span>{service.phone}</span>
                        </div>
                      </div>

                      <Button className="w-full bg-yellow-400 text-black hover:bg-yellow-500">Learn More</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Success Stories */}
        <section className="py-20 bg-gray-50">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Success Stories</h2>
              <p className="mt-4 text-lg text-gray-600">Real people, real transformations, real hope</p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {successStories.map((story, index) => (
                <Card key={index} className="h-full">
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 relative h-24 w-24 rounded-full overflow-hidden">
                      <Image src={story.image || "/placeholder.svg"} alt={story.name} fill className="object-cover" />
                    </div>
                    <CardTitle className="text-lg">{story.name}</CardTitle>
                    <CardDescription>Age {story.age}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-gray-600 italic">"{story.story}"</p>

                      <div>
                        <h4 className="font-semibold text-sm text-gray-800 mb-2">Services received:</h4>
                        <div className="flex flex-wrap gap-1">
                          {story.services.map((service, idx) => (
                            <span key={idx} className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                              {service}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="pt-3 border-t border-gray-100">
                        <p className="text-sm font-medium text-green-600">✓ {story.outcome}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* How to Access Services */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">
                How to Access Our Services
              </h2>
              <p className="mt-4 text-lg text-gray-600">Getting help is easier than you think</p>
            </div>

            <div className="grid gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Phone className="h-8 w-8 text-black" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">1. Get in Touch</h3>
                <p className="text-gray-600">
                  Call our helpline, visit our office, or get referred by a professional. We're here 24/7 for
                  emergencies.
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 bg-yellow-400 rounded-full flex items-center justify-center">
                  <Calendar className="h-8 w-8 text-black" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">2. Assessment</h3>
                <p className="text-gray-600">
                  We'll meet with you to understand your needs and create a personalized support plan together.
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 bg-yellow-400 rounded-full flex items-center justify-center">
                  <MapPin className="h-8 w-8 text-black" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">3. Support Journey</h3>
                <p className="text-gray-600">
                  Begin your journey with dedicated support workers who will be with you every step of the way.
                </p>
              </div>
            </div>

            <div className="mt-12 text-center">
              <Button asChild size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500">
                <Link href="/contact">Start Your Journey Today</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* CTA Section with Background */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gray-900"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.pexels.com/photos/1438072/pexels-photo-1438072.jpeg"
              alt="Community support"
              fill
              className="object-cover opacity-30"
            />
          </div>
          <div className="container relative z-10 text-center">
            <div className="backdrop-blur-md bg-black/20 border border-white/10 shadow-xl rounded-3xl p-8 md:p-12 max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to Transform Lives Together?
              </h2>
              <p className="mt-4 text-lg text-white/90">
                Join our mission to provide comprehensive support services that rebuild lives and strengthen
                communities.
              </p>
              <div className="mt-8 flex flex-wrap justify-center gap-4">
                <Button asChild size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500 shadow-lg">
                  <Link href="/get-involved">Get Involved</Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                >
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Emergency Contact */}
        <section className="py-12 bg-red-50 border-l-4 border-red-400">
          <div className="container">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-red-800 mb-4">Need Emergency Help?</h3>
              <p className="text-red-700 mb-6">If you're in immediate need of housing or support, don't wait.</p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button size="lg" className="bg-red-600 text-white hover:bg-red-700">
                  <Phone className="h-4 w-4 mr-2" />
                  Call Emergency Line: 0800 123 4567
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                >
                  <Link href="/contact">Visit Our Office</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
