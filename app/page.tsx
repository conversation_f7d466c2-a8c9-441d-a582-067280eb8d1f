"use client"

import { useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, useScroll, useTransform } from "framer-motion"
import { Heart, Home, Users, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import CountUp from "@/components/count-up"
import TestimonialSlider from "@/components/testimonial-slider"
import ServicesSection from "@/components/services-section"
import PropertySlider from "@/components/property-slider"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"
import StarlightLogo from "@/components/starlight-logo"


export default function HomePage() {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  })

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "30%"])
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [1, 0.8, 0.6])

  return (
    <>
      <SiteHeader />
      <main className="flex-1 overflow-hidden">
        {/* Hero Section - Brand Guidelines Compliant */}
        <section ref={ref} className="relative min-h-screen overflow-hidden bg-oxford-blue brand-pattern">
          <div className="absolute inset-0 bg-gradient-to-r from-oxford-blue via-oxford-blue/50 to-transparent z-10"></div>

          {/* Background Image */}
          <motion.div style={{ y, opacity }} className="absolute inset-0 z-0">
            <Image
              src="https://i0.wp.com/news.gardner-white.com/wp-content/uploads/full-shot-happy-family-home-scaled.jpg?resize=1080,720&ssl=1"
              alt="Starlight Housing - A Place to Call Home"
              fill
              priority
              className="object-cover object-right"
            />
          </motion.div>

          <div className="container relative z-20 flex min-h-screen md:pl-28 flex-col justify-center text-white">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-2xl"
            >
            

              <h1 className="text-5xl md:text-7xl font-black tracking-tight mb-6 title-font">
                <span className="text-starlight-yellow">A Place to</span>
                <br />
                <span className="text-white">Call Home</span>
              </h1>

              <p className="text-lg md:text-xl text-white/90 mb-8 leading-relaxed lead-font">
                Starlight Housing provides holistic support, fostering dignity and well-being for those at risk of
                homelessness.
              </p>

              <div className="flex items-center gap-4 mb-12">
                <Button
                  asChild
                  size="lg"
                  className="bg-starlight-yellow text-oxford-blue hover:bg-yellow-500 text-lg px-8 py-6 h-auto lead-font font-semibold"
                >
                  <Link href="/properties" className="flex items-center gap-2">
                    Your Journey Home Starts Here
                    <ArrowRight className="h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Who We Are Section - Brand Guidelines */}
        <section className="py-20 bg-snow-white">
          <div className="container">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl md:text-5xl font-black text-oxford-blue mb-6 title-font">Who We Are?</h2>
                <div className="space-y-6 text-lg text-gray-700 body-font">
                  <p>
                    Our organization is dedicated to providing safe, secure accommodation and holistic support for
                    vulnerable individuals within the community who are at risk of homelessness.
                  </p>
                  <p>
                    We strive to create a stable environment where individuals can regain their independence, access
                    essential services, and build a brighter future.
                  </p>
                  <p>
                    Through person-centered care, advocacy, and community partnerships, we work to prevent homelessness
                    and promote long-term well-being for those most in need.
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="relative rounded-3xl overflow-hidden border-4 border-starlight-yellow shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <Image
                    src="https://images.unsplash.com/photo-1736386347519-4cfe9980ca43?q=80&w=1548&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt="Support worker helping someone in need"
                    width={600}
                    height={400}
                    className="object-cover w-full h-80"
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Our Vision Section - Brand Guidelines Design */}
        <section className="py-20 bg-oxford-blue brand-pattern relative overflow-hidden">
          {/* <div className="absolute inset-0 opacity-10">
            <div
              className="w-full h-full"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23F8D613' fillOpacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: "60px 60px",
              }}
            ></div>
          </div> */}

          <div className="container relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-6xl md:text-8xl font-black text-starlight-yellow mb-16">Our Vision</h2>

              <div className="relative max-w-4xl mx-auto mb-16">
                <div className="flex justify-center items-center relative h-80">
                  <motion.div
                    initial={{ opacity: 0, x: -100, rotate: -15 }}
                    whileInView={{ opacity: 1, x: 0, rotate: -10 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    viewport={{ once: true }}
                    className="absolute left-0 z-10"
                  >
                    <div className="w-72 h-48 rounded-3xl border-4 border-carolina-blue overflow-hidden shadow-2xl transform -rotate-12">
                      <Image
                        src="https://images.unsplash.com/photo-1523847027398-d3eb27914c67?q=80&w=1740&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        alt="Person experiencing homelessness"
                        width={600}
                        height={400}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 100 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="absolute z-20"
                  >
                    <div className="w-72 h-48 rounded-3xl border-4 border-starlight-yellow overflow-hidden shadow-2xl transform rotate-6">
                      <Image
                        src="https://images.unsplash.com/photo-1525898416124-a6464ad4dd84?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDE1fHx8ZW58MHx8fHx8"
                        alt="Community support"
                        width={600}
                        height={400}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 100, rotate: 15 }}
                    whileInView={{ opacity: 1, x: 0, rotate: 12 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    viewport={{ once: true }}
                    className="absolute right-0 z-10"
                  >
                    <div className="w-72 h-48 rounded-3xl border-4 border-snow-white overflow-hidden shadow-2xl transform rotate-12">
                      <Image
                        src="https://images.unsplash.com/photo-1619195623000-0a6e1ab08eaa?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDMxfHx8ZW58MHx8fHx8"
                        alt="Hope and recovery"
                        width={600}
                        height={400}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  </motion.div>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                viewport={{ once: true }}
                className="max-w-2xl mx-auto mb-12"
              >
                <p className="text-2xl md:text-3xl text-white font-medium leading-relaxed">
                  A community where no one
                  <br />
                  faces homelessness alone.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 1 }}
                viewport={{ once: true }}
                className="flex justify-center"
              >
                <div className="w-16 h-16 opacity-60">
                  <StarlightLogo size="md" variant="mark" color="white" />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Our Mission Section */}
        <section className="py-20 bg-snow-white">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-black text-oxford-blue mb-6 title-font">Our Mission</h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto lead-font">
                To support vulnerable individuals with safe housing and the help they need to avoid homelessness.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 items-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="relative">
                  <div className="absolute -top-4 -left-4 w-full h-full rounded-3xl border-4 border-carolina-blue transform rotate-3"></div>
                  <div className="relative rounded-3xl overflow-hidden border-4 border-starlight-yellow shadow-xl">
                    <Image
                      src="https://images.pexels.com/photos/7551668/pexels-photo-7551668.jpeg"
                      alt="Support worker with client"
                      width={500}
                      height={350}
                      className="object-cover w-full h-80"
                    />
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                  <h3 className="text-2xl font-bold text-oxford-blue mb-4 title-font">Our Approach</h3>
                  <ul className="space-y-3 text-gray-700 body-font">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-starlight-yellow rounded-full mt-2"></div>
                      <span>Person-centered care and support</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-starlight-yellow rounded-full mt-2"></div>
                      <span>Advocacy and empowerment</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-starlight-yellow rounded-full mt-2"></div>
                      <span>Community partnerships</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-starlight-yellow rounded-full mt-2"></div>
                      <span>Long-term well-being focus</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Enhanced Statistics Section */}
        <section className="relative py-20 bg-oxford-blue brand-pattern">
          <div className="container relative z-10">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4 title-font">Making a Difference</h2>
              <p className="text-white/80 text-lg lead-font">Our impact supporting vulnerable individuals</p>
            </div>
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3">
              <CountUp
                end={5000}
                suffix="+"
                title="Vulnerable Individuals Supported"
                description="People helped off the streets"
                icon={<Users className="h-10 w-10 text-starlight-yellow" />}
              />
              <CountUp
                end={200}
                suffix="+"
                title="Safe Homes Provided"
                description="From crisis to stability"
                icon={<Home className="h-10 w-10 text-starlight-yellow" />}
              />
              <CountUp
                end={50}
                title="Community Partnerships"
                description="Working together to end homelessness"
                icon={<Heart className="h-10 w-10 text-starlight-yellow" />}
              />
            </div>
          </div>
        </section>

        {/* Featured Properties Section */}
        <section className="py-20 bg-snow-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-oxford-blue title-font">
                Safe Housing Solutions
              </h2>
              <p className="mt-4 text-lg text-gray-600 lead-font">
                From emergency shelter to permanent homes - providing dignity and safety for all
              </p>
            </div>
            <PropertySlider />
          </div>
        </section>

        {/* Our Services Section */}
        {/* <section className="bg-gray-50 py-20">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-oxford-blue title-font">
                Our Services
              </h2>
              <p className="mt-4 text-lg text-gray-600 lead-font">Comprehensive support for vulnerable individuals</p>
            </div>
            <ServicesSection />
          </div>
        </section> */}

        {/* Testimonials Section */}
        <section className="relative py-20 bg-starlight-yellow">
          <div className="container relative z-10">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-oxford-blue title-font">
                Stories of Hope
              </h2>
              <p className="mt-4 text-lg text-oxford-blue/80 lead-font">Real journeys from homelessness to home</p>
            </div>
            <TestimonialSlider />
          </div>
        </section>

        {/* Enhanced CTA Banner */}
        <section className="relative overflow-hidden py-20">
          <div className="absolute inset-0 bg-gray-900"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.unsplash.com/photo-1509059852496-f3822ae057bf?q=80&w=1545&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="Community support helping homeless individuals"
              fill
              className="object-cover opacity-30"
            />
          </div>
          <div className="container relative z-10">
            <div className="mx-auto max-w-3xl text-center">
              <div className="backdrop-blur-sm bg-black/20 border border-white/10 shadow-xl rounded-3xl p-8 md:p-12">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-white title-font">
                  Help Us End Homelessness in the UK
                </h2>
                <p className="mt-4 text-lg text-white/90 lead-font">
                  Join our mission to provide safe housing and support for vulnerable individuals facing homelessness.
                </p>
                <div className="mt-8 flex flex-wrap justify-center gap-4">
                  <Button
                    asChild
                    size="lg"
                    className="bg-starlight-yellow text-oxford-blue hover:bg-yellow-500 shadow-lg lead-font font-semibold"
                  >
                    <Link href="/get-involved">Get Involved</Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 lead-font"
                  >
                    <Link href="/contact">Contact Us</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>

    
  )
}
