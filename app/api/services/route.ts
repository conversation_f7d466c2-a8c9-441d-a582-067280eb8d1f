import { NextRequest, NextResponse } from 'next/server'
import { DataService } from '@/lib/data-service'
import type { ServiceFilters } from '@/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters from query parameters
    const filters: ServiceFilters = {}
    
    const contactPerson = searchParams.get('contactPerson')
    if (contactPerson) filters.contactPerson = contactPerson
    
    const category = searchParams.get('category')
    if (category) filters.category = category
    
    const services = await DataService.getServices(filters)
    
    return NextResponse.json({
      success: true,
      data: services,
      count: services.length
    })
  } catch (error) {
    console.error('Services API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch services',
        data: []
      },
      { status: 500 }
    )
  }
}
