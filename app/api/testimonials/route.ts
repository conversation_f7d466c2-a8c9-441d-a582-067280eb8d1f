import { NextRequest, NextResponse } from 'next/server'
import { DataService } from '@/lib/data-service'

export async function GET(request: NextRequest) {
  try {
    const testimonials = await DataService.getTestimonials()
    
    return NextResponse.json({
      success: true,
      data: testimonials,
      count: testimonials.length
    })
  } catch (error) {
    console.error('Testimonials API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch testimonials',
        data: []
      },
      { status: 500 }
    )
  }
}
