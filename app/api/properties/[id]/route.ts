import { NextRequest, NextResponse } from 'next/server'
import { DataService } from '@/lib/data-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const property = await DataService.getPropertyById(params.id)
    
    if (!property) {
      return NextResponse.json(
        {
          success: false,
          error: 'Property not found',
          data: null
        },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: property
    })
  } catch (error) {
    console.error('Property API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch property',
        data: null
      },
      { status: 500 }
    )
  }
}
