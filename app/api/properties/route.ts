import { NextRequest, NextResponse } from 'next/server'
import { DataService } from '@/lib/data-service'
import type { PropertyFilters } from '@/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters from query parameters
    const filters: PropertyFilters = {}
    
    const location = searchParams.get('location')
    if (location) filters.location = location
    
    const bedrooms = searchParams.get('bedrooms')
    if (bedrooms) filters.bedrooms = parseInt(bedrooms)
    
    const features = searchParams.get('features')
    if (features) filters.features = features.split(',')
    
    const properties = await DataService.getProperties(filters)
    
    return NextResponse.json({
      success: true,
      data: properties,
      count: properties.length
    })
  } catch (error) {
    console.error('Properties API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch properties',
        data: []
      },
      { status: 500 }
    )
  }
}

// Handle POST requests for property search/filtering
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const filters: PropertyFilters = body.filters || {}
    
    const properties = await DataService.getProperties(filters)
    
    return NextResponse.json({
      success: true,
      data: properties,
      count: properties.length,
      filters: filters
    })
  } catch (error) {
    console.error('Properties POST API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to filter properties',
        data: []
      },
      { status: 500 }
    )
  }
}
