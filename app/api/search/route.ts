import { NextRequest, NextResponse } from 'next/server'
import { DataService } from '@/lib/data-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'Search query must be at least 2 characters long',
          data: null
        },
        { status: 400 }
      )
    }
    
    const results = await DataService.searchContent(query.trim())
    
    const totalResults = 
      results.properties.length + 
      results.services.length + 
      results.testimonials.length + 
      results.teamMembers.length
    
    return NextResponse.json({
      success: true,
      data: results,
      query: query.trim(),
      totalResults
    })
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Search failed',
        data: null
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, filters } = body
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: 'Search query must be at least 2 characters long',
          data: null
        },
        { status: 400 }
      )
    }
    
    const results = await DataService.searchContent(query.trim())
    
    // Apply additional filters if provided
    let filteredResults = results
    if (filters) {
      // Add custom filtering logic here if needed
    }
    
    const totalResults = 
      filteredResults.properties.length + 
      filteredResults.services.length + 
      filteredResults.testimonials.length + 
      filteredResults.teamMembers.length
    
    return NextResponse.json({
      success: true,
      data: filteredResults,
      query: query.trim(),
      filters,
      totalResults
    })
  } catch (error) {
    console.error('Search POST API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Search failed',
        data: null
      },
      { status: 500 }
    )
  }
}
