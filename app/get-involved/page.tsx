import Image from "next/image"
import Link from "next/link"
import { Heart, Users, Calendar, GraduationCap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"

const volunteerOpportunities = [
  {
    icon: <Heart className="h-8 w-8 text-yellow-500" />,
    title: "Peer Mentor",
    description: "Support individuals on their housing journey using your own lived experience.",
    commitment: "4-6 hours per week",
    requirements: [
      "Lived experience of housing challenges",
      "Completed our training program",
      "Empathetic and patient",
    ],
    impact: "Help 3-5 people monthly",
  },
  {
    icon: <Users className="h-8 w-8 text-yellow-500" />,
    title: "Community Companion",
    description: "Provide friendship and social support to reduce isolation.",
    commitment: "2-3 hours per week",
    requirements: ["Good listening skills", "Reliable and punctual", "Background check required"],
    impact: "Reduce loneliness for vulnerable individuals",
  },
  {
    icon: <GraduationCap className="h-8 w-8 text-yellow-500" />,
    title: "Skills Trainer",
    description: "Teach practical life skills like cooking, budgeting, or digital literacy.",
    commitment: "3-4 hours per week",
    requirements: ["Expertise in specific skill area", "Teaching or training experience", "Patient and encouraging"],
    impact: "Empower people with essential life skills",
  },
  {
    icon: <Calendar className="h-8 w-8 text-yellow-500" />,
    title: "Event Organizer",
    description: "Help organize community events and social activities.",
    commitment: "Flexible, project-based",
    requirements: ["Event planning experience", "Creative and organized", "Team player"],
    impact: "Build stronger communities",
  },
]

const partnershipOpportunities = [
  {
    title: "Corporate Partnerships",
    description: "Partner with us to provide employment opportunities, skills training, or financial support.",
    benefits: ["Employee volunteering programs", "CSR impact reporting", "Community engagement"],
    examples: ["Job placement programs", "Skills workshops", "Mentorship schemes"],
  },
  {
    title: "Healthcare Partnerships",
    description: "Collaborate to provide integrated health and housing support services.",
    benefits: ["Improved patient outcomes", "Reduced hospital readmissions", "Holistic care approach"],
    examples: ["Mental health support", "Addiction recovery programs", "Health screenings"],
  },
  {
    title: "Educational Partnerships",
    description: "Work with us to provide training, education, and research opportunities.",
    benefits: ["Real-world learning experiences", "Research opportunities", "Community impact"],
    examples: ["Student placements", "Research projects", "Training programs"],
  },
]

const donationImpact = [
  { amount: "£10", impact: "Provides a welcome pack for a new resident" },
  { amount: "£25", impact: "Funds a counseling session for someone in crisis" },
  { amount: "£50", impact: "Covers utilities for a week for a family" },
  { amount: "£100", impact: "Supports a person through their first month of independent living" },
  { amount: "£250", impact: "Funds emergency accommodation for a week" },
  { amount: "£500", impact: "Provides comprehensive support for someone for a month" },
]

export default function GetInvolvedPage() {
  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gray-800">
          <div className="absolute inset-0 bg-black/40"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.pexels.com/photos/1157557/pexels-photo-1157557.jpeg"
              alt="Volunteers helping in community"
              fill
              className="object-cover"
            />
          </div>
          <div className="container relative z-10 text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Get Involved</h1>
            <p className="mt-6 mx-auto max-w-2xl text-lg text-white/90">
              Join our community of supporters helping vulnerable people rebuild their lives through safe housing and
              compassionate support.
            </p>
          </div>
        </section>

        {/* Volunteer Opportunities */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Volunteer Opportunities</h2>
              <p className="mt-4 text-lg text-gray-600">Make a direct impact in people's lives through volunteering</p>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              {volunteerOpportunities.map((opportunity, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="mb-4">{opportunity.icon}</div>
                    <CardTitle className="text-xl text-gray-800">{opportunity.title}</CardTitle>
                    <CardDescription className="text-gray-600">{opportunity.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="font-semibold text-sm text-gray-800 mb-1">Time Commitment:</p>
                      <p className="text-sm text-gray-600">{opportunity.commitment}</p>
                    </div>

                    <div>
                      <p className="font-semibold text-sm text-gray-800 mb-2">Requirements:</p>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {opportunity.requirements.map((req, idx) => (
                          <li key={idx} className="flex items-start">
                            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2 mt-2"></div>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="pt-3 border-t border-gray-100">
                      <p className="text-sm font-medium text-green-600">Impact: {opportunity.impact}</p>
                    </div>

                    <Button className="w-full bg-yellow-400 text-black hover:bg-yellow-500">Apply to Volunteer</Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="mt-12 text-center">
              <p className="text-gray-600 mb-6">
                Don't see the right opportunity? We'd love to hear about your skills and interests.
              </p>
              <Button asChild variant="outline" size="lg">
                <Link href="/contact">Contact Our Volunteer Coordinator</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Donation Impact */}
        <section className="py-20 bg-gray-50">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Make a Donation</h2>
              <p className="mt-4 text-lg text-gray-600">
                Every donation directly supports people in their journey to stable housing
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
              {donationImpact.map((item, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold text-yellow-600">{item.amount}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600">{item.impact}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                <Button size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500">
                  Donate £10
                </Button>
                <Button size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500">
                  Donate £25
                </Button>
                <Button size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500">
                  Donate £50
                </Button>
                <Button size="lg" variant="outline">
                  Custom Amount
                </Button>
              </div>
              <p className="text-sm text-gray-600">
                All donations are secure and go directly to supporting vulnerable individuals.
                <Link href="/about" className="text-yellow-600 hover:underline ml-1">
                  Learn how we use donations
                </Link>
              </p>
            </div>
          </div>
        </section>

        {/* Partnership Opportunities */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Partnership Opportunities</h2>
              <p className="mt-4 text-lg text-gray-600">
                Collaborate with us to create lasting change in your community
              </p>
            </div>

            <div className="grid gap-8 lg:grid-cols-3">
              {partnershipOpportunities.map((partnership, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <CardTitle className="text-xl text-gray-800">{partnership.title}</CardTitle>
                    <CardDescription className="text-gray-600">{partnership.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-sm text-gray-800 mb-2">Benefits for Partners:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {partnership.benefits.map((benefit, idx) => (
                          <li key={idx} className="flex items-start">
                            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-2 mt-2"></div>
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-sm text-gray-800 mb-2">Examples:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {partnership.examples.map((example, idx) => (
                          <li key={idx} className="flex items-start">
                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2 mt-2"></div>
                            {example}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Button className="w-full bg-yellow-400 text-black hover:bg-yellow-500">Explore Partnership</Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Success Stories from Volunteers */}
        <section className="py-20 bg-gray-50">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Volunteer Stories</h2>
              <p className="mt-4 text-lg text-gray-600">Hear from our amazing volunteers about their experiences</p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 relative h-20 w-20 rounded-full overflow-hidden">
                    <Image
                      src="https://images.pexels.com/photos/1065084/pexels-photo-1065084.jpeg"
                      alt="Rachel Green"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardTitle className="text-lg text-gray-800">Rachel Green</CardTitle>
                  <CardDescription className="text-gray-600">Peer Mentor, 2 years</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 italic mb-4">
                    "Having experienced homelessness myself, I understand the challenges. Being able to support others
                    through their journey and see them thrive has been incredibly rewarding."
                  </p>
                  <p className="text-sm font-medium text-green-600">Supported 15+ individuals to independent living</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 relative h-20 w-20 rounded-full overflow-hidden">
                    <Image
                      src="https://images.pexels.com/photos/612999/pexels-photo-612999.jpeg"
                      alt="David Wilson"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardTitle className="text-lg text-gray-800">David Wilson</CardTitle>
                  <CardDescription className="text-gray-600">Skills Trainer, 1 year</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 italic mb-4">
                    "Teaching cooking classes has been amazing. Watching someone prepare their first proper meal and
                    seeing their confidence grow is priceless."
                  </p>
                  <p className="text-sm font-medium text-green-600">Trained 50+ people in essential life skills</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 relative h-20 w-20 rounded-full overflow-hidden">
                    <Image
                      src="https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg"
                      alt="Sophie Martinez"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardTitle className="text-lg text-gray-800">Sophie Martinez</CardTitle>
                  <CardDescription className="text-gray-600">Community Companion, 3 years</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 italic mb-4">
                    "Sometimes people just need someone to listen. The friendships I've built through volunteering have
                    enriched my life as much as theirs."
                  </p>
                  <p className="text-sm font-medium text-green-600">Provided companionship to 25+ individuals</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Get Started Section with Background */}
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gray-900"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.pexels.com/photos/1438072/pexels-photo-1438072.jpeg"
              alt="Community volunteers working together"
              fill
              className="object-cover opacity-30"
            />
          </div>
          <div className="container relative z-10 text-center">
            <div className="backdrop-blur-md bg-black/20 border border-white/10 shadow-xl rounded-3xl p-8 md:p-12 max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">Ready to Make a Difference?</h2>
              <p className="mt-4 text-lg text-white/90">
                Whether you have 2 hours a week or want to become a major partner, there's a way for you to help
                transform lives.
              </p>
              <div className="mt-8 flex flex-wrap justify-center gap-4">
                <Button asChild size="lg" className="bg-yellow-400 text-black hover:bg-yellow-500 shadow-lg">
                  <Link href="/contact">Start Volunteering</Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                >
                  <Link href="/contact">Discuss Partnership</Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                >
                  <Link href="/contact">Make a Donation</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
