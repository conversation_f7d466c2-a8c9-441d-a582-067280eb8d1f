"use client"

import type React from "react"

import { useState } from "react"
import { MapPin, Phone, Mail, Clock, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    urgency: "",
    subject: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Form submitted:", formData)
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gray-800">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container relative z-10 text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Get in Touch</h1>
            <p className="mt-6 mx-auto max-w-2xl text-lg text-white/90">
              We're here to help. Reach out to us and let's start your journey to safe, supportive housing.
            </p>
          </div>
        </section>

        {/* Emergency Banner */}
        <section className="py-6 bg-red-600 text-white">
          <div className="container text-center">
            <p className="text-lg font-semibold">
              🚨 Emergency Housing Needed? Call our 24/7 helpline:
              <a href="tel:08001234567" className="ml-2 underline hover:no-underline">
                0800 123 4567
              </a>
            </p>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="grid gap-8 lg:grid-cols-2">
              {/* Contact Form */}
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-6">Send us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleChange("name", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleChange("phone", e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange("email", e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="urgency" className="block text-sm font-medium text-gray-700 mb-2">
                      How urgent is your situation? *
                    </label>
                    <Select value={formData.urgency} onValueChange={(value) => handleChange("urgency", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select urgency level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="emergency">Emergency - Need help today</SelectItem>
                        <SelectItem value="urgent">Urgent - Need help within a week</SelectItem>
                        <SelectItem value="soon">Soon - Need help within a month</SelectItem>
                        <SelectItem value="planning">Planning - Looking ahead</SelectItem>
                        <SelectItem value="general">General inquiry</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      What can we help you with? *
                    </label>
                    <Select value={formData.subject} onValueChange={(value) => handleChange("subject", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a topic" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="housing">Housing Application</SelectItem>
                        <SelectItem value="emergency">Emergency Accommodation</SelectItem>
                        <SelectItem value="support">Support Services</SelectItem>
                        <SelectItem value="mental-health">Mental Health Support</SelectItem>
                        <SelectItem value="benefits">Benefits Assistance</SelectItem>
                        <SelectItem value="referral">Professional Referral</SelectItem>
                        <SelectItem value="volunteer">Volunteering</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Tell us more about your situation *
                    </label>
                    <Textarea
                      id="message"
                      rows={6}
                      value={formData.message}
                      onChange={(e) => handleChange("message", e.target.value)}
                      placeholder="Please share any details that would help us understand how we can best support you..."
                      required
                    />
                  </div>

                  <Button type="submit" size="lg" className="w-full bg-yellow-400 text-black hover:bg-yellow-500">
                    <Send className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>

                  <p className="text-sm text-gray-600">
                    * Required fields. We'll respond within 24 hours, or immediately for emergencies.
                  </p>
                </form>
              </div>

              {/* Contact Information Cards */}
              <div className="space-y-6">
                <h2 className="text-3xl font-bold text-gray-800 mb-6">Contact Information</h2>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Phone className="h-5 w-5 text-yellow-500" />
                      Phone Support
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="font-semibold text-red-600">Emergency Helpline (24/7)</p>
                      <p className="text-lg">0800 123 4567</p>
                    </div>
                    <div>
                      <p className="font-semibold">General Inquiries</p>
                      <p>0161 234 5678</p>
                      <p className="text-sm text-gray-600">Mon-Fri: 9am-5pm</p>
                    </div>
                    <div>
                      <p className="font-semibold">Support Services</p>
                      <p>0161 234 5679</p>
                      <p className="text-sm text-gray-600">Mon-Fri: 8am-6pm</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5 text-yellow-500" />
                      Email Support
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="font-semibold">General Inquiries</p>
                      <p><EMAIL></p>
                    </div>
                    <div>
                      <p className="font-semibold">Housing Applications</p>
                      <p><EMAIL></p>
                    </div>
                    <div>
                      <p className="font-semibold">Support Services</p>
                      <p><EMAIL></p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-yellow-500" />
                      Visit Our Offices
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="font-semibold">Main Office</p>
                      <p>123 Housing Street</p>
                      <p>Manchester, M1 2AB</p>
                    </div>
                    <div>
                      <p className="font-semibold">Birmingham Office</p>
                      <p>456 Support Avenue</p>
                      <p>Birmingham, B1 3CD</p>
                    </div>
                    <div>
                      <p className="font-semibold">London Office</p>
                      <p>789 Community Road</p>
                      <p>London, E1 4EF</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-yellow-500" />
                      Opening Hours
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Monday - Friday</span>
                        <span>9:00 AM - 5:00 PM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Saturday</span>
                        <span>10:00 AM - 2:00 PM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sunday</span>
                        <span>Closed</span>
                      </div>
                      <div className="pt-2 border-t">
                        <p className="text-sm text-red-600 font-semibold">Emergency support available 24/7</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Alternative Contact Methods */}
        <section className="py-20 bg-gray-50">
          <div className="container">
            <h2 className="text-3xl font-bold text-gray-800 text-center mb-12">Other Ways to Reach Us</h2>

            <div className="grid gap-8 md:grid-cols-3">
              <Card className="text-center">
                <CardHeader>
                  <CardTitle>Walk-in Services</CardTitle>
                  <CardDescription>No appointment needed</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Visit any of our offices during opening hours. Our reception team can help with immediate needs or
                    schedule appointments.
                  </p>
                  <Button variant="outline" className="w-full">
                    Find Nearest Office
                  </Button>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <CardTitle>Professional Referrals</CardTitle>
                  <CardDescription>For social workers, healthcare providers</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Dedicated referral system for professionals. Fast-track assessments and priority placement for
                    urgent cases.
                  </p>
                  <Button variant="outline" className="w-full">
                    Referral Portal
                  </Button>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <CardTitle>Online Chat</CardTitle>
                  <CardDescription>Live support during office hours</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Chat with our support team online for quick questions or guidance on services and applications.
                  </p>
                  <Button variant="outline" className="w-full">
                    Start Chat
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
