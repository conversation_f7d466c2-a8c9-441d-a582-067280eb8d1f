import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, MapPin, Bed, Bath, Star, Phone, Mail, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import SiteHeader from '@/components/site-header'
import SiteFooter from '@/components/site-footer'
import { DataService } from '@/lib/data-service'
import { useImageAsset } from '@/hooks/use-data'
import type { Property } from '@/types'

interface PropertyPageProps {
  params: {
    id: string
  }
}

// This is a server component, so we'll fetch data directly
async function getProperty(id: string): Promise<Property | null> {
  return await DataService.getPropertyById(id)
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  const property = await getProperty(params.id)

  if (!property) {
    notFound()
  }

  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        {/* Back Navigation */}
        <section className="py-6 bg-gray-50">
          <div className="container">
            <Link 
              href="/properties" 
              className="inline-flex items-center gap-2 text-oxford-blue hover:text-starlight-yellow transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Properties
            </Link>
          </div>
        </section>

        {/* Property Hero */}
        <section className="py-12 bg-white">
          <div className="container">
            <div className="grid gap-8 lg:grid-cols-2">
              {/* Property Image */}
              <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden">
                <PropertyImage property={property} />
                <div className="absolute top-4 left-4">
                  <Badge className="bg-starlight-yellow text-oxford-blue text-lg px-4 py-2">
                    {property.price}
                  </Badge>
                </div>
                <div className="absolute bottom-4 left-4 flex items-center gap-1 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                  <Star className="h-4 w-4 fill-starlight-yellow text-starlight-yellow" />
                  <span className="text-white font-medium">{property.rating}</span>
                </div>
              </div>

              {/* Property Details */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-oxford-blue mb-2">
                    {property.title}
                  </h1>
                  <div className="flex items-center gap-2 text-gray-600 mb-4">
                    <MapPin className="h-5 w-5" />
                    <span className="text-lg">{property.location}</span>
                  </div>
                </div>

                {/* Property Stats */}
                <div className="flex items-center gap-6 text-gray-600">
                  <div className="flex items-center gap-2">
                    <Bed className="h-5 w-5" />
                    <span className="font-medium">{property.bedrooms} Bedroom{property.bedrooms !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Bath className="h-5 w-5" />
                    <span className="font-medium">{property.bathrooms} Bathroom{property.bathrooms !== 1 ? 's' : ''}</span>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <h2 className="text-xl font-semibold text-oxford-blue mb-3">About This Property</h2>
                  <p className="text-gray-600 leading-relaxed">{property.description}</p>
                </div>

                {/* Features */}
                <div>
                  <h2 className="text-xl font-semibold text-oxford-blue mb-3">Features & Amenities</h2>
                  <div className="flex flex-wrap gap-2">
                    {property.features.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-sm">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Contact CTA */}
                <div className="pt-4">
                  <Button 
                    size="lg" 
                    className="w-full bg-starlight-yellow text-oxford-blue hover:bg-yellow-500 font-semibold"
                  >
                    <Phone className="h-5 w-5 mr-2" />
                    Contact About This Property
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Support Information */}
        {(property.supportWorker || property.residents) && (
          <section className="py-12 bg-gray-50">
            <div className="container">
              <div className="grid gap-6 md:grid-cols-2">
                {/* Support Worker */}
                {property.supportWorker && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5 text-starlight-yellow" />
                        Support Worker
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-4">
                        {property.supportWorkerImage && (
                          <div className="relative h-12 w-12 rounded-full overflow-hidden">
                            <SupportWorkerImage 
                              imageKey={property.supportWorkerImage} 
                              name={property.supportWorker} 
                            />
                          </div>
                        )}
                        <div>
                          <p className="font-semibold text-oxford-blue">{property.supportWorker}</p>
                          <p className="text-sm text-gray-600">Dedicated Support Specialist</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Current Residents */}
                {property.residents && property.residents.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5 text-starlight-yellow" />
                        Community
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {property.residents.map((resident, index) => (
                          <p key={index} className="text-gray-600">{resident}</p>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </section>
        )}

        {/* Contact Section */}
        <section className="py-12 bg-oxford-blue text-white">
          <div className="container text-center">
            <h2 className="text-2xl lg:text-3xl font-bold mb-4">
              Interested in This Property?
            </h2>
            <p className="text-white/80 mb-8 max-w-2xl mx-auto">
              Our team is here to help you with your housing needs. Contact us to learn more about this property and our support services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-starlight-yellow text-oxford-blue hover:bg-yellow-500"
                asChild
              >
                <Link href="/contact">
                  <Mail className="h-5 w-5 mr-2" />
                  Get in Touch
                </Link>
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-oxford-blue"
                asChild
              >
                <Link href="/properties">
                  View All Properties
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}

// Client component for property image
function PropertyImage({ property }: { property: Property }) {
  'use client'
  const imageAsset = useImageAsset(property.image, 'properties')
  
  return (
    <Image
      src={imageAsset.src}
      alt={imageAsset.alt}
      fill
      className="object-cover"
      priority
    />
  )
}

// Client component for support worker image
function SupportWorkerImage({ imageKey, name }: { imageKey: string, name: string }) {
  'use client'
  const imageAsset = useImageAsset(imageKey, 'team')
  
  return (
    <Image
      src={imageAsset.src}
      alt={`${name} - Support Worker`}
      fill
      className="object-cover"
    />
  )
}
