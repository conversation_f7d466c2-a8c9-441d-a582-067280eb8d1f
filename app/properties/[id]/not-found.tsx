import Link from 'next/link'
import { Home, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import SiteHeader from '@/components/site-header'
import SiteFooter from '@/components/site-footer'

export default function PropertyNotFound() {
  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        <section className="py-20 bg-gray-50">
          <div className="container text-center">
            <div className="max-w-md mx-auto">
              <div className="mb-8">
                <Home className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h1 className="text-3xl font-bold text-oxford-blue mb-2">
                  Property Not Found
                </h1>
                <p className="text-gray-600">
                  Sorry, we couldn't find the property you're looking for. It may have been moved or is no longer available.
                </p>
              </div>
              
              <div className="space-y-4">
                <Button 
                  asChild 
                  className="w-full bg-starlight-yellow text-oxford-blue hover:bg-yellow-500"
                >
                  <Link href="/properties">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    View All Properties
                  </Link>
                </Button>
                
                <Button 
                  asChild 
                  variant="outline" 
                  className="w-full"
                >
                  <Link href="/">
                    <Home className="h-4 w-4 mr-2" />
                    Go Home
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
