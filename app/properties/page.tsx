import Image from "next/image"
import Link from "next/link"
import { MapPin, Bed, Bath, Star, Heart, Filter, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"

const properties = [
  {
    id: 1,
    title: "Riverside Sanctuary",
    location: "Manchester, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.9,
    image: "https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg",
    price: "Available",
    features: ["Fully Furnished", "Support Services", "Community Garden"],
    description: "A peaceful riverside home with modern amenities and 24/7 support services.",
    residents: ["<PERSON>, 28", "<PERSON>, 34"],
    supportWorker: "<PERSON>",
    supportWorkerImage: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",
  },
  {
    id: 2,
    title: "City Center Haven",
    location: "Birmingham, UK",
    bedrooms: 1,
    bathrooms: 1,
    rating: 4.8,
    image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg",
    price: "Available",
    features: ["Transport Links", "Medical Support", "Shared Spaces"],
    description: "Modern studio apartment in the heart of the city with excellent transport connections.",
    residents: ["James, 25"],
    supportWorker: "David Wilson",
    supportWorkerImage: "https://images.pexels.com/photos/612999/pexels-photo-612999.jpeg",
  },
  {
    id: 3,
    title: "Garden View Residence",
    location: "Leeds, UK",
    bedrooms: 3,
    bathrooms: 2,
    rating: 4.9,
    image: "https://images.pexels.com/photos/1438072/pexels-photo-1438072.jpeg",
    price: "Available",
    features: ["Family Friendly", "Garden Access", "Support Network"],
    description: "Spacious family home with beautiful garden views and comprehensive support services.",
    residents: ["Maria & children", "Ahmed, 42"],
    supportWorker: "Lisa Chen",
    supportWorkerImage: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg",
  },
  {
    id: 4,
    title: "Coastal Retreat",
    location: "Brighton, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.7,
    image: "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg",
    price: "Available",
    features: ["Sea Views", "Wellness Programs", "Community Hub"],
    description: "Tranquil coastal property with stunning sea views and wellness-focused support.",
    residents: ["Anna, 31", "Robert, 45"],
    supportWorker: "Sophie Martinez",
    supportWorkerImage: "https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg",
  },
  {
    id: 5,
    title: "Urban Oasis",
    location: "London, UK",
    bedrooms: 1,
    bathrooms: 1,
    rating: 4.6,
    image: "https://images.pexels.com/photos/1571453/pexels-photo-1571453.jpeg",
    price: "Available",
    features: ["Central Location", "Job Support", "Life Skills Training"],
    description: "Modern apartment with excellent access to employment opportunities and training.",
    residents: ["Carlos, 29"],
    supportWorker: "Rachel Green",
    supportWorkerImage: "https://images.pexels.com/photos/1065084/pexels-photo-1065084.jpeg",
  },
  {
    id: 6,
    title: "Community House",
    location: "Glasgow, UK",
    bedrooms: 4,
    bathrooms: 2,
    rating: 4.8,
    image: "https://images.pexels.com/photos/1080696/pexels-photo-1080696.jpeg",
    price: "Available",
    features: ["Shared Living", "Peer Support", "Skills Development"],
    description: "Supportive shared living environment with focus on community and personal growth.",
    residents: ["Multiple residents", "Peer mentors"],
    supportWorker: "Ian MacLeod",
    supportWorkerImage: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg",
  },
]

export default function PropertiesPage() {
  return (
    <>
      <SiteHeader />
      <main className="flex-1 pt-16">
        {/* Hero Section */}
        <section className="relative py-20 bg-gray-800">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute inset-0 z-0">
            <Image
              src="https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg"
              alt="Beautiful housing community"
              fill
              className="object-cover opacity-30"
            />
          </div>
          <div className="container relative z-10 text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Find Your Home</h1>
            <p className="mt-6 mx-auto max-w-2xl text-lg text-white/90">
              Discover safe, supportive housing options designed around the people we serve and their unique journeys.
            </p>
          </div>
        </section>

        {/* Search and Filters */}
        <section className="py-12 bg-gray-50">
          <div className="container">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <div className="grid gap-4 md:grid-cols-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input placeholder="Search by location..." className="pl-10 text-gray-200 placeholder:text-gray-200" />
                </div>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Property Type" className="text-gray-200 placeholder:text-gray-200" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="studio">Studio</SelectItem>
                    <SelectItem value="1bed">1 Bedroom</SelectItem>
                    <SelectItem value="2bed">2 Bedroom</SelectItem>
                    <SelectItem value="3bed">3+ Bedroom</SelectItem>
                    <SelectItem value="shared">Shared Living</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Support Type" className="text-gray-200 placeholder:text-gray-200" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mental-health">Mental Health</SelectItem>
                    <SelectItem value="addiction">Addiction Recovery</SelectItem>
                    <SelectItem value="youth">Youth Support</SelectItem>
                    <SelectItem value="family">Family Support</SelectItem>
                    <SelectItem value="employment">Employment Help</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="bg-yellow-400 text-black hover:bg-yellow-500">
                  <Filter className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Properties Grid */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gray-800">Available Properties</h2>
              <p className="mt-4 text-lg text-gray-600">
                Each home is more than just a place to stay - it's a community where people rebuild their lives
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {properties.map((property) => (
                <div
                  key={property.id}
                  className="bg-white border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:shadow-xl transition-shadow"
                >
                  <div className="relative h-64">
                    <Image
                      src={property.image || "/placeholder.svg"}
                      alt={property.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-yellow-400 text-black">{property.price}</Badge>
                    </div>
                    <div className="absolute top-4 right-4">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="bg-white/20 backdrop-blur-sm rounded-full text-white hover:text-yellow-400"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="absolute bottom-4 left-4 flex items-center gap-1 text-white">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{property.rating}</span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800">{property.title}</h3>
                    <div className="mt-2 flex items-center gap-1 text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">{property.location}</span>
                    </div>

                    <div className="mt-4 flex items-center gap-4 text-gray-600">
                      <div className="flex items-center gap-1">
                        <Bed className="h-4 w-4" />
                        <span className="text-sm">{property.bedrooms} bed</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Bath className="h-4 w-4" />
                        <span className="text-sm">{property.bathrooms} bath</span>
                      </div>
                    </div>

                    <p className="mt-3 text-sm text-gray-600">{property.description}</p>

                    {/* People Information */}
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-semibold text-gray-800 mb-2">Community</h4>
                      <p className="text-xs text-gray-600 mb-1">
                        <strong>Current residents:</strong> {property.residents.join(", ")}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="relative h-6 w-6 rounded-full overflow-hidden">
                          <Image
                            src={property.supportWorkerImage || "/placeholder.svg"}
                            alt={property.supportWorker}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <p className="text-xs text-gray-600">
                          <strong>Support worker:</strong> {property.supportWorker}
                        </p>
                      </div>
                    </div>

                    <div className="mt-4 flex flex-wrap gap-2">
                      {property.features.map((feature, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>

                    <div className="mt-6 flex gap-2">
                      <Button asChild className="flex-1 bg-yellow-400 text-black hover:bg-yellow-500">
                        <Link href={`/properties/${property.id}`}>View Details</Link>
                      </Button>
                      <Button asChild variant="outline" className="flex-1">
                        <Link href="/contact">Apply Now</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-12 text-center">
              <Button variant="outline" size="lg">
                Load More Properties
              </Button>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="relative py-20 bg-yellow-400">
          <div className="container text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-800 sm:text-4xl">
              Need Help Finding the Right Home?
            </h2>
            <p className="mt-4 mx-auto max-w-2xl text-lg text-gray-700">
              Our support team is here to help match you with the perfect housing solution for your unique situation.
            </p>
            <div className="mt-8 flex flex-wrap justify-center gap-4">
              <Button asChild size="lg" className="bg-gray-800 text-white hover:bg-gray-900">
                <Link href="/contact">Speak to an Advisor</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="border-gray-800 text-gray-800 hover:bg-gray-800 hover:text-white"
              >
                <Link href="/services">View Support Services</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
