import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import SiteHeader from "@/components/site-header"
import SiteFooter from "@/components/site-footer"

export default function AboutPage() {
  return (
    <>
      <SiteHeader />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative py-20 bg-gray-800">
          <div className="container relative z-10 text-center text-white">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">About Starlight Housing</h1>
            <p className="mt-6 mx-auto max-w-2xl text-lg text-white/90">
              Providing compassionate housing solutions and support services for vulnerable individuals across the UK.
            </p>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold tracking-tight text-gray-800">Our Mission & Vision</h2>
                <p className="mt-4 text-lg text-gray-600">
                  At Starlight Housing, we believe that everyone deserves a safe place to call home. Our mission is to
                  provide secure, supportive housing for vulnerable individuals and families.
                </p>
                <p className="mt-4 text-lg text-gray-600">
                  Our vision is a society where no one faces homelessness alone, and where everyone has access to the
                  support they need to thrive.
                </p>
              </div>
              <div className="relative h-80 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="https://images.pexels.com/photos/7551659/pexels-photo-7551659.jpeg"
                  alt="Our mission in action"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20 bg-gray-50">
          <div className="container">
            <h2 className="text-3xl font-bold tracking-tight text-center text-gray-800 mb-12">Our Story</h2>
            <div className="max-w-3xl mx-auto">
              <div className="relative border-l-4 border-yellow-400 pl-8 pb-12">
                <div className="absolute -left-2 top-0 h-4 w-4 rounded-full bg-yellow-400"></div>
                <h3 className="text-xl font-bold text-gray-800">2010: The Beginning</h3>
                <p className="mt-2 text-gray-600">
                  Starlight Housing was founded by a group of social workers who saw firsthand the challenges faced by
                  vulnerable individuals seeking safe housing.
                </p>
              </div>
              <div className="relative border-l-4 border-yellow-400 pl-8 pb-12">
                <div className="absolute -left-2 top-0 h-4 w-4 rounded-full bg-yellow-400"></div>
                <h3 className="text-xl font-bold text-gray-800">2015: Expanding Our Reach</h3>
                <p className="mt-2 text-gray-600">
                  We expanded our services to include mental health support, benefits assistance, and life skills
                  training.
                </p>
              </div>
              <div className="relative border-l-4 border-yellow-400 pl-8">
                <div className="absolute -left-2 top-0 h-4 w-4 rounded-full bg-yellow-400"></div>
                <h3 className="text-xl font-bold text-gray-800">Today: Making a Difference</h3>
                <p className="mt-2 text-gray-600">
                  Today, Starlight Housing operates over 200 properties, supporting thousands of individuals on their
                  journey to independence and stability.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Meet the Team - Simplified */}
        <section className="py-20 bg-white">
          <div className="container">
            <h2 className="text-3xl font-bold tracking-tight text-center text-gray-800 mb-12">Meet Our Team</h2>
            <div className="grid gap-8 md:grid-cols-3">
              {[
                {
                  name: "Sarah Johnson",
                  role: "Executive Director",
                  bio: "With over 15 years in social services, Sarah leads our organization with compassion and vision.",
                },
                {
                  name: "Michael Chen",
                  role: "Housing Director",
                  bio: "Michael oversees our property portfolio and ensures all our homes meet the highest standards.",
                },
                {
                  name: "Amina Patel",
                  role: "Support Services Manager",
                  bio: "Amina coordinates our comprehensive support services, focusing on resident wellbeing.",
                },
              ].map((member, index) => (
                <div key={index} className="bg-white border border-gray-100 shadow-md rounded-2xl overflow-hidden p-6">
                  <h3 className="text-xl font-bold text-gray-800">{member.name}</h3>
                  <p className="text-yellow-500 font-medium">{member.role}</p>
                  <p className="mt-2 text-gray-600">{member.bio}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section - Simplified */}
        <section className="py-20 bg-yellow-400">
          <div className="container text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-800 sm:text-4xl">Ready to make a difference?</h2>
            <p className="mt-4 mx-auto max-w-2xl text-lg text-gray-700">
              Join us in our mission to provide safe housing and support for vulnerable individuals.
            </p>
            <div className="mt-8 flex flex-wrap justify-center gap-4">
              <Button asChild size="lg" className="bg-gray-800 text-white hover:bg-gray-900">
                <Link href="/get-involved">Get Involved</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="bg-gray-800 text-white"
              >
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <SiteFooter />
    </>
  )
}
