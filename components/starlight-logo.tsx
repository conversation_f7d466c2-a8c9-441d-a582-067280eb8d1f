import Image from "next/image"

interface StarlightLogoProps {
  className?: string
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "horizontal" | "vertical" | "mark"
  color?: "yellow" | "white" | "blue"
}

export default function StarlightLogo({
  className = "",
  size = "md",
  variant = "horizontal",
  color = "yellow",
}: StarlightLogoProps) {
  const sizeClasses = {
    sm: "h-8",
    md: "h-10",
    lg: "h-12",
    xl: "h-16",
  }

  const colorClasses = {
    yellow: "text-starlight-yellow",
    white: "text-white",
    blue: "text-oxford-blue",
  }

  const LogoMark = () => (
    <div className={`${sizeClasses[size]} aspect-square relative`}>
      {/* Crescent Moon */}
      <svg viewBox="0 0 40 40" fill="none" className={`w-full h-full ${colorClasses[color]}`}>
        {/* Crescent Moon Shape */}
        <path
          d="M20 4C28.837 4 36 11.163 36 20C36 28.837 28.837 36 20 36C15.029 36 10.686 33.314 8.343 29.314C11.686 30.771 15.686 30.771 19.029 29.314C24.343 26.4 26.4 19.771 23.486 14.457C21.029 10.457 16.686 8.4 12.686 9.857C14.4 6.4 17.029 4 20 4Z"
          fill="currentColor"
        />
        {/* Star Shape */}
        <path
          d="M20 14L21.545 18.455L26 20L21.545 21.545L20 26L18.455 21.545L14 20L18.455 18.455L20 14Z"
          fill="currentColor"
        />
      </svg>
    </div>
  )

  if (variant === "mark") {
    return <LogoMark />
  }

  if (variant === "vertical") {
    return (
      <div className={`flex flex-col items-center gap-2 ${className}`}>
        <LogoMark />
        <span
          className={`font-bold ${colorClasses[color]} ${size === "sm" ? "text-sm" : size === "lg" ? "text-xl" : size === "xl" ? "text-2xl" : "text-lg"}`}
        >
          Starlight Housing
        </span>
      </div>
    )
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <Image
        src="/1.png"
        alt="Starlight Housing Logo"
        width={60}
        height={60}
        className="object-cover"
      />
      <span
        className={`font-bold ${colorClasses[color]} ${size === "sm" ? "text-sm" : size === "lg" ? "text-xl" : size === "xl" ? "text-2xl" : "text-lg"}`}
      >
        Starlight Housing
      </span>
    </div>
  )
}
