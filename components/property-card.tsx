import Image from "next/image"
import Link from "next/link"
import { MapPin, Bed, Bath, Star, Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useImageAsset } from "@/hooks/use-data"
import type { Property } from "@/types"

interface PropertyCardProps {
  property: Property
  variant?: 'default' | 'compact' | 'featured'
  showActions?: boolean
  onViewDetails?: (property: Property) => void
  onFavorite?: (property: Property) => void
}

export default function PropertyCard({ 
  property, 
  variant = 'default',
  showActions = true,
  onViewDetails,
  onFavorite
}: PropertyCardProps) {
  const imageAsset = useImageAsset(property.image, 'properties')

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(property)
    }
    // If no custom handler, we'll use Link navigation instead
  }

  const handleFavorite = () => {
    if (onFavorite) {
      onFavorite(property)
    }
  }

  if (variant === 'compact') {
    return (
      <div className="bg-white border border-gray-100 shadow-md rounded-xl overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative h-48">
          <Image
            src={imageAsset.src}
            alt={imageAsset.alt}
            fill
            className="object-cover"
          />
          <div className="absolute top-3 left-3">
            <Badge className="bg-yellow-400 text-black text-xs">{property.price}</Badge>
          </div>
          {showActions && (
            <div className="absolute top-3 right-3">
              <Button
                size="icon"
                variant="ghost"
                className="bg-white/20 backdrop-blur-sm rounded-full text-white hover:text-yellow-400 h-8 w-8"
                onClick={handleFavorite}
              >
                <Heart className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        <div className="p-4">
          <h3 className="font-semibold text-gray-800 line-clamp-1">{property.title}</h3>
          <div className="mt-1 flex items-center gap-1 text-gray-600">
            <MapPin className="h-3 w-3" />
            <span className="text-xs">{property.location}</span>
          </div>
          <div className="mt-2 flex items-center gap-3 text-gray-600">
            <div className="flex items-center gap-1">
              <Bed className="h-3 w-3" />
              <span className="text-xs">{property.bedrooms}</span>
            </div>
            <div className="flex items-center gap-1">
              <Bath className="h-3 w-3" />
              <span className="text-xs">{property.bathrooms}</span>
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span className="text-xs">{property.rating}</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'featured') {
    return (
      <div className="w-full max-w-4xl backdrop-blur-md bg-white/90 border border-white/30 shadow-2xl overflow-hidden rounded-2xl">
        <div className="grid md:grid-cols-2">
          <div className="relative h-64 md:h-full">
            <Image
              src={imageAsset.src}
              alt={imageAsset.alt}
              fill
              className="object-cover"
            />
            <div className="absolute top-4 left-4">
              <Badge className="bg-yellow-400 text-black">{property.price}</Badge>
            </div>
            <div className="absolute bottom-4 left-4 flex items-center gap-1 text-white">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">{property.rating}</span>
            </div>
          </div>

          <div className="p-8">
            <h3 className="text-2xl font-bold text-gray-800">{property.title}</h3>
            <div className="mt-2 flex items-center gap-1 text-gray-600">
              <MapPin className="h-4 w-4" />
              <span className="text-sm">{property.location}</span>
            </div>

            <div className="mt-4 flex items-center gap-4 text-gray-600">
              <div className="flex items-center gap-1">
                <Bed className="h-4 w-4" />
                <span className="text-sm">{property.bedrooms} bed</span>
              </div>
              <div className="flex items-center gap-1">
                <Bath className="h-4 w-4" />
                <span className="text-sm">{property.bathrooms} bath</span>
              </div>
            </div>

            <p className="mt-4 text-gray-600">{property.description}</p>

            <div className="mt-6 flex flex-wrap gap-2">
              {property.features.map((feature, idx) => (
                <Badge key={idx} variant="secondary" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>

            {showActions && (
              <>
                {onViewDetails ? (
                  <Button
                    className="mt-8 w-full bg-yellow-400 text-black hover:bg-yellow-500"
                    onClick={handleViewDetails}
                  >
                    View Details
                  </Button>
                ) : (
                  <Button
                    asChild
                    className="mt-8 w-full bg-yellow-400 text-black hover:bg-yellow-500"
                  >
                    <Link href={`/properties/${property.id}`}>
                      View Details
                    </Link>
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Default variant
  return (
    <div className="bg-white border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:shadow-xl transition-shadow">
      <div className="relative h-64">
        <Image
          src={imageAsset.src}
          alt={imageAsset.alt}
          fill
          className="object-cover"
        />
        <div className="absolute top-4 left-4">
          <Badge className="bg-yellow-400 text-black">{property.price}</Badge>
        </div>
        {showActions && (
          <div className="absolute top-4 right-4">
            <Button
              size="icon"
              variant="ghost"
              className="bg-white/20 backdrop-blur-sm rounded-full text-white hover:text-yellow-400"
              onClick={handleFavorite}
            >
              <Heart className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-800">{property.title}</h3>
            <div className="mt-1 flex items-center gap-1 text-gray-600">
              <MapPin className="h-4 w-4" />
              <span className="text-sm">{property.location}</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{property.rating}</span>
          </div>
        </div>

        <div className="mt-4 flex items-center gap-4 text-gray-600">
          <div className="flex items-center gap-1">
            <Bed className="h-4 w-4" />
            <span className="text-sm">{property.bedrooms} bed</span>
          </div>
          <div className="flex items-center gap-1">
            <Bath className="h-4 w-4" />
            <span className="text-sm">{property.bathrooms} bath</span>
          </div>
        </div>

        <p className="mt-4 text-gray-600 text-sm line-clamp-2">{property.description}</p>

        <div className="mt-4 flex flex-wrap gap-2">
          {property.features.slice(0, 3).map((feature, idx) => (
            <Badge key={idx} variant="secondary" className="text-xs">
              {feature}
            </Badge>
          ))}
          {property.features.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{property.features.length - 3} more
            </Badge>
          )}
        </div>

        {showActions && (
          <div className="mt-6 flex gap-2">
            {onViewDetails ? (
              <Button
                className="flex-1 bg-yellow-400 text-black hover:bg-yellow-500"
                onClick={handleViewDetails}
              >
                View Details
              </Button>
            ) : (
              <Button
                asChild
                className="flex-1 bg-yellow-400 text-black hover:bg-yellow-500"
              >
                <Link href={`/properties/${property.id}`}>
                  View Details
                </Link>
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
