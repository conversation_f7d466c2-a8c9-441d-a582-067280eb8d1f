"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, MapPin, Bed, Bath, Star } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"

const properties = [
  {
    id: 1,
    title: "Riverside Sanctuary",
    location: "Manchester, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.9,
    image: "https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg",
    price: "Available",
    features: ["Fully Furnished", "Support Services", "Community Garden"],
    description: "A peaceful riverside home with modern amenities and 24/7 support services.",
  },
  {
    id: 2,
    title: "City Center Haven",
    location: "Birmingham, UK",
    bedrooms: 1,
    bathrooms: 1,
    rating: 4.8,
    image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg",
    price: "Available",
    features: ["Transport Links", "Medical Support", "Shared Spaces"],
    description: "Modern studio apartment in the heart of the city with excellent transport connections.",
  },
  {
    id: 3,
    title: "Garden View Residence",
    location: "Leeds, UK",
    bedrooms: 3,
    bathrooms: 2,
    rating: 4.9,
    image: "https://images.pexels.com/photos/1438072/pexels-photo-1438072.jpeg",
    price: "Available",
    features: ["Family Friendly", "Garden Access", "Support Network"],
    description: "Spacious family home with beautiful garden views and comprehensive support services.",
  },
  {
    id: 4,
    title: "Coastal Retreat",
    location: "Brighton, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.7,
    image: "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg",
    price: "Available",
    features: ["Sea Views", "Wellness Programs", "Community Hub"],
    description: "Tranquil coastal property with stunning sea views and wellness-focused support.",
  },
]

export default function PropertySlider() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const nextSlide = () => {
    if (isAnimating) return
    setDirection(1)
    setCurrentIndex((prevIndex) => (prevIndex + 1) % properties.length)
  }

  const prevSlide = () => {
    if (isAnimating) return
    setDirection(-1)
    setCurrentIndex((prevIndex) => (prevIndex - 1 + properties.length) % properties.length)
  }

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(interval)
  }, [isAnimating])

  const variants = {
    enter: (direction: number) => {
      return {
        x: direction > 0 ? 1000 : -1000,
        opacity: 0,
        scale: 0.8,
      }
    },
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
    exit: (direction: number) => {
      return {
        x: direction < 0 ? 1000 : -1000,
        opacity: 0,
        scale: 0.8,
        transition: {
          duration: 0.5,
        },
      }
    },
  }

  const property = properties[currentIndex]

  return (
    <div className="relative mx-auto max-w-5xl">
      <div className="overflow-hidden rounded-3xl">
        <div className="relative h-[600px] w-full">
          <AnimatePresence initial={false} custom={direction} onExitComplete={() => setIsAnimating(false)}>
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              onAnimationStart={() => setIsAnimating(true)}
              onAnimationComplete={() => setIsAnimating(false)}
              className="absolute inset-0 flex items-center justify-center"
            >
              <div className="w-full max-w-4xl backdrop-blur-md bg-white/90 border border-white/30 shadow-2xl overflow-hidden rounded-2xl">
                <div className="grid md:grid-cols-2">
                  <div className="relative h-64 md:h-full">
                    <Image
                      src={property.image || "/placeholder.svg"}
                      alt={property.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-yellow-400 text-black">{property.price}</Badge>
                    </div>
                    <div className="absolute bottom-4 left-4 flex items-center gap-1 text-white">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{property.rating}</span>
                    </div>
                  </div>

                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-800">{property.title}</h3>
                    <div className="mt-2 flex items-center gap-1 text-gray-600">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">{property.location}</span>
                    </div>

                    <div className="mt-4 flex items-center gap-4 text-gray-600">
                      <div className="flex items-center gap-1">
                        <Bed className="h-4 w-4" />
                        <span className="text-sm">{property.bedrooms} bed</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Bath className="h-4 w-4" />
                        <span className="text-sm">{property.bathrooms} bath</span>
                      </div>
                    </div>

                    <p className="mt-4 text-gray-600">{property.description}</p>

                    <div className="mt-6 flex flex-wrap gap-2">
                      {property.features.map((feature, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>

                    <Button className="mt-8 w-full bg-yellow-400 text-black hover:bg-yellow-500">View Details</Button>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="backdrop-blur-md bg-white/80 border border-white/30 rounded-full text-gray-700 hover:bg-white"
          onClick={prevSlide}
          disabled={isAnimating}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>

        <div className="flex gap-2">
          {properties.map((_, index) => (
            <button
              key={index}
              className={`h-2 w-8 rounded-full transition-all ${
                index === currentIndex ? "bg-yellow-400" : "bg-gray-300"
              }`}
              onClick={() => {
                if (isAnimating) return
                setDirection(index > currentIndex ? 1 : -1)
                setCurrentIndex(index)
              }}
            />
          ))}
        </div>

        <Button
          variant="outline"
          size="icon"
          className="backdrop-blur-md bg-white/80 border border-white/30 rounded-full text-gray-700 hover:bg-white"
          onClick={nextSlide}
          disabled={isAnimating}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>
    </div>
  )
}
