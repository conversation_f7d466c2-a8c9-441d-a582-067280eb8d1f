"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import { useProperties } from "@/hooks/use-data"
import PropertyCard from "@/components/property-card"

export default function PropertySlider() {
  const { data: properties, loading, error } = useProperties()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const nextSlide = () => {
    if (isAnimating || !properties || properties.length === 0) return
    setDirection(1)
    setCurrentIndex((prevIndex) => (prevIndex + 1) % properties.length)
  }

  const prevSlide = () => {
    if (isAnimating || !properties || properties.length === 0) return
    setDirection(-1)
    setCurrentIndex((prevIndex) => (prevIndex - 1 + properties.length) % properties.length)
  }

  useEffect(() => {
    if (!properties || properties.length === 0) return

    const interval = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(interval)
  }, [isAnimating, properties?.length])

  // Show loading state
  if (loading) {
    return (
      <div className="relative mx-auto max-w-5xl">
        <div className="overflow-hidden rounded-3xl">
          <div className="relative h-[600px] w-full flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading properties...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !properties || properties.length === 0) {
    return (
      <div className="relative mx-auto max-w-5xl">
        <div className="overflow-hidden rounded-3xl">
          <div className="relative h-[600px] w-full flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <p className="text-gray-600">
                {error ? `Error loading properties: ${error}` : 'No properties available'}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const variants = {
    enter: (direction: number) => {
      return {
        x: direction > 0 ? 1000 : -1000,
        opacity: 0,
        scale: 0.8,
      }
    },
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
      },
    },
    exit: (direction: number) => {
      return {
        x: direction < 0 ? 1000 : -1000,
        opacity: 0,
        scale: 0.8,
        transition: {
          duration: 0.5,
        },
      }
    },
  }

  const property = properties[currentIndex]

  return (
    <div className="relative mx-auto max-w-5xl">
      <div className="overflow-hidden rounded-3xl">
        <div className="relative h-[600px] w-full">
          <AnimatePresence initial={false} custom={direction} onExitComplete={() => setIsAnimating(false)}>
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              onAnimationStart={() => setIsAnimating(true)}
              onAnimationComplete={() => setIsAnimating(false)}
              className="absolute inset-0 flex items-center justify-center"
            >
              <PropertyCard
                property={property}
                variant="featured"
                onViewDetails={(prop) => {
                  // Handle view details - could navigate to property page
                  console.log('View details for:', prop.id)
                }}
              />
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="backdrop-blur-md bg-white/80 border border-white/30 rounded-full text-gray-700 hover:bg-white"
          onClick={prevSlide}
          disabled={isAnimating}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>

        <div className="flex gap-2">
          {properties.map((_, index) => (
            <button
              key={index}
              className={`h-2 w-8 rounded-full transition-all ${
                index === currentIndex ? "bg-yellow-400" : "bg-gray-300"
              }`}
              onClick={() => {
                if (isAnimating) return
                setDirection(index > currentIndex ? 1 : -1)
                setCurrentIndex(index)
              }}
            />
          ))}
        </div>

        <Button
          variant="outline"
          size="icon"
          className="backdrop-blur-md bg-white/80 border border-white/30 rounded-full text-gray-700 hover:bg-white"
          onClick={nextSlide}
          disabled={isAnimating}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>
    </div>
  )
}
