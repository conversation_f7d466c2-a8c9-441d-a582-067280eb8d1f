import Link from "next/link"
import Starlight<PERSON>ogo from "@/components/starlight-logo"

export default function SiteFooter() {
  return (
    <footer className="bg-oxford-blue text-white py-12 brand-pattern">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="mb-4">
              <StarlightLogo size="md" variant="horizontal" color="white" />
            </div>
            <p className="text-white/80 body-font">
              Providing compassionate housing solutions and support services for vulnerable individuals.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-bold mb-4 title-font">Quick Links</h3>
            <ul className="space-y-2 body-font">
              <li>
                <Link href="/" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  About
                </Link>
              </li>
              <li>
                <Link href="/properties" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Properties
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Services
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-bold mb-4 title-font">Get Involved</h3>
            <ul className="space-y-2 body-font">
              <li>
                <Link href="/get-involved" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Volunteer
                </Link>
              </li>
              <li>
                <Link href="/get-involved" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Donate
                </Link>
              </li>
              <li>
                <Link href="/get-involved" className="text-white/80 hover:text-starlight-yellow transition-colors">
                  Partner With Us
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-bold mb-4 title-font">Contact</h3>
            <address className="not-italic text-white/80 body-font">
              <p>123 Housing Street</p>
              <p>London, UK</p>
              <p className="mt-2"><EMAIL></p>
              <p>+44 20 1234 5678</p>
            </address>
          </div>
        </div>
        <div className="border-t border-white/20 mt-8 pt-8 text-center text-white/60 body-font">
          <p>&copy; {new Date().getFullYear()} Starlight Housing. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
