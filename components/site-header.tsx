"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"
import StarlightLogo from "@/components/starlight-logo"
import { Menu, X } from "lucide-react"

export default function SiteHeader() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled ? "bg-snow-white/10 backdrop-blur-md border-b border-oxford-blue/10 shadow-lg" : "bg-transparent",
      )}
    >
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center">
          <StarlightLogo size="md" variant="horizontal" color={isScrolled ? "blue" : "white"} />
        </Link>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden md:block">
          <NavigationMenuList>
            <NavigationMenuItem>
              <Link href="/" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10",
                    isScrolled ? "text-yellow-500 hover:text-yellow-500" : "text-white hover:text-white",
                  )}
                >
                  Home
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/about" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10",
                    isScrolled ? "text-oxford-blue hover:text-oxford-blue" : "text-white hover:text-white",
                  )}
                >
                  About
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger
                className={cn(
                  "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10 active:bg-white/10",
                  isScrolled ? "text-oxford-blue hover:text-oxford-blue" : "text-white hover:text-white",
                )}
              >
                Services
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px] bg-snow-white/95 backdrop-blur-md border border-oxford-blue/10">
                  <li className="row-span-3">
                    <NavigationMenuLink asChild>
                      <a
                        className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-starlight-yellow to-yellow-500 p-6 no-underline outline-none focus:shadow-md"
                        href="/services"
                      >
                        <div className="mt-4 mb-2 text-lg font-bold text-oxford-blue title-font">Our Services</div>
                        <p className="text-sm leading-tight text-oxford-blue/80 body-font">
                          Comprehensive support for those in need of housing and assistance.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <a
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-starlight-yellow/20 hover:text-oxford-blue focus:bg-starlight-yellow/20 focus:text-oxford-blue"
                        href="/services#emergency"
                      >
                        <div className="text-sm font-medium leading-none lead-font">Emergency Housing</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground body-font">
                          Immediate shelter for those in crisis situations.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <a
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-starlight-yellow/20 hover:text-oxford-blue focus:bg-starlight-yellow/20 focus:text-oxford-blue"
                        href="/services#transitional"
                      >
                        <div className="text-sm font-medium leading-none lead-font">Transitional Housing</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground body-font">
                          Supportive temporary accommodation with path to permanence.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <a
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-starlight-yellow/20 hover:text-oxford-blue focus:bg-starlight-yellow/20 focus:text-oxford-blue"
                        href="/services#support"
                      >
                        <div className="text-sm font-medium leading-none lead-font">Support Services</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground body-font">
                          Counseling, job training, and life skills development.
                        </p>
                      </a>
                    </NavigationMenuLink>
                  </li>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/properties" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10 active:bg-white/10",
                    isScrolled ? "text-oxford-blue hover:text-oxford-blue" : "text-white hover:text-white",
                  )}
                >
                  Properties
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/get-involved" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10 active:bg-white/10",
                    isScrolled ? "text-oxford-blue hover:text-oxford-blue" : "text-white hover:text-white",
                  )}
                >
                  Get Involved
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/contact" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    "bg-transparent hover:bg-white/10 transition-colors duration-300 body-font focus:bg-white/10 active:bg-white/10",
                    isScrolled ? "text-oxford-blue hover:text-oxford-blue" : "text-white hover:text-white",
                  )}
                >
                  Contact
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        {/* Mobile menu button */}
        <button
          className="md:hidden p-2 rounded-md focus:outline-none"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? (
            <X className={isScrolled ? "text-oxford-blue" : "text-white"} size={24} />
          ) : (
            <Menu className={isScrolled ? "text-oxford-blue" : "text-white"} size={24} />
          )}
        </button>

        {/* Desktop Buttons */}
        <div className="hidden md:flex items-center gap-4">
          <Button
            variant="outline"
            className={cn(
              "transition-all duration-300 body-font",
              "border-oxford-blue bg-oxford-blue text-white hover:bg-oxford-blue hover:text-snow-white"
            )}
          >
            Donate
          </Button>
          <Button className="bg-starlight-yellow text-oxford-blue hover:bg-yellow-500 body-font font-medium">
            Get Help
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-snow-white/95 backdrop-blur-md border-t border-oxford-blue/10 shadow-lg">
          <div className="container py-4 space-y-2">
            <Link
              href="/"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              href="/about"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>
            <Link
              href="/services"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Services
            </Link>
            <Link
              href="/properties"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Properties
            </Link>
            <Link
              href="/get-involved"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Get Involved
            </Link>
            <Link
              href="/contact"
              className={cn(
                "block w-full p-3 rounded-md transition-colors duration-300 body-font",
                "text-oxford-blue hover:bg-oxford-blue/10",
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
            </Link>
            <div className="flex flex-col gap-2 pt-2">
              <Button
                variant="outline"
                className="w-full border-oxford-blue bg-oxford-blue text-white hover:bg-oxford-blue/90 body-font"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Donate
              </Button>
              <Button
                className="w-full bg-starlight-yellow text-oxford-blue hover:bg-yellow-500 body-font font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Get Help
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}