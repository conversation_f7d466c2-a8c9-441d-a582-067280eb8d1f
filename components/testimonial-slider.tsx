"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const testimonials = [
  {
    id: 1,
    content:
      "<PERSON><PERSON> helped me find peace and safety when I had nowhere else to turn. Their support changed my life.",
    author: "<PERSON>",
    role: "Housing Support Recipient",
    image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg",
  },
  {
    id: 2,
    content: "Their team made us feel like family. The comprehensive support they provide goes beyond just housing.",
    author: "<PERSON>",
    role: "Community Resident",
    image: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg",
  },
  {
    id: 3,
    content:
      "The dedication of the Starlight team is unmatched. They truly care about making a difference in people's lives.",
    author: "<PERSON>",
    role: "Former Resident, Now Volunteer",
    image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg",
  },
]

export default function TestimonialSlider() {
  const [current, setCurrent] = useState(0)

  const next = () => {
    setCurrent((current) => (current === testimonials.length - 1 ? 0 : current + 1))
  }

  const prev = () => {
    setCurrent((current) => (current === 0 ? testimonials.length - 1 : current - 1))
  }

  useEffect(() => {
    const interval = setInterval(() => {
      next()
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="relative mx-auto max-w-4xl">
      <div className="overflow-hidden rounded-lg">
        <div className="relative h-full w-full">
          <div className="bg-white/95 backdrop-blur-sm border border-oxford-blue/10 shadow-xl rounded-2xl">
            <div className="p-8 md:p-12">
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 text-4xl font-bold text-oxford-blue">"</div>
                <p className="mb-6 text-lg md:text-xl text-oxford-blue">{testimonials[current].content}</p>
                <Avatar className="h-16 w-16 mb-4">
                  <AvatarImage src={testimonials[current].image || "/placeholder.svg"} />
                  <AvatarFallback className="bg-starlight-yellow/20 text-oxford-blue">
                    {testimonials[current].author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="mt-4">
                  <p className="font-semibold text-oxford-blue">{testimonials[current].author}</p>
                  <p className="text-sm text-gray-600">{testimonials[current].role}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-center space-x-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full ${index === current ? "bg-oxford-blue" : "bg-oxford-blue/30"}`}
            onClick={() => setCurrent(index)}
          />
        ))}
      </div>

      <Button
        variant="outline"
        size="icon"
        className="absolute -left-4 top-1/2 -translate-y-1/2 rounded-full border-oxford-blue/20 bg-white/80 shadow-md hover:bg-white md:-left-6"
        onClick={prev}
      >
        <ChevronLeft className="h-5 w-5 text-oxford-blue" />
        <span className="sr-only">Previous testimonial</span>
      </Button>

      <Button
        variant="outline"
        size="icon"
        className="absolute -right-4 top-1/2 -translate-y-1/2 rounded-full border-oxford-blue/20 bg-white/80 shadow-md hover:bg-white md:-right-6"
        onClick={next}
      >
        <ChevronRight className="h-5 w-5 text-oxford-blue" />
        <span className="sr-only">Next testimonial</span>
      </Button>
    </div>
  )
}
