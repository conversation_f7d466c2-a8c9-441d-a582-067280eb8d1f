"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useTestimonials, useImageAsset } from "@/hooks/use-data"

export default function TestimonialSlider() {
  const { data: testimonials, loading, error } = useTestimonials()
  const [current, setCurrent] = useState(0)

  // Always call useImageAsset hook, but handle the case when testimonials might not be loaded
  const currentTestimonial = testimonials?.[current]
  const imageAsset = useImageAsset(currentTestimonial?.image || 'placeholder', 'testimonials')

  const next = () => {
    if (!testimonials || testimonials.length === 0) return
    setCurrent((current) => (current === testimonials.length - 1 ? 0 : current + 1))
  }

  const prev = () => {
    if (!testimonials || testimonials.length === 0) return
    setCurrent((current) => (current === 0 ? testimonials.length - 1 : current - 1))
  }

  useEffect(() => {
    if (!testimonials || testimonials.length === 0) return

    const interval = setInterval(() => {
      next()
    }, 5000)

    return () => clearInterval(interval)
  }, [testimonials?.length])

  // Show loading state
  if (loading) {
    return (
      <div className="relative mx-auto max-w-4xl">
        <div className="overflow-hidden rounded-lg">
          <div className="bg-white/95 backdrop-blur-sm border border-oxford-blue/10 shadow-xl rounded-2xl">
            <div className="p-8 md:p-12 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-oxford-blue mx-auto"></div>
                <p className="mt-4 text-oxford-blue">Loading testimonials...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !testimonials || testimonials.length === 0) {
    return (
      <div className="relative mx-auto max-w-4xl">
        <div className="overflow-hidden rounded-lg">
          <div className="bg-white/95 backdrop-blur-sm border border-oxford-blue/10 shadow-xl rounded-2xl">
            <div className="p-8 md:p-12 text-center">
              <p className="text-oxford-blue">
                {error ? `Error loading testimonials: ${error}` : 'No testimonials available'}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // At this point we know testimonials exist and currentTestimonial is valid

  return (
    <div className="relative mx-auto max-w-4xl">
      <div className="overflow-hidden rounded-lg">
        <div className="relative h-full w-full">
          <div className="bg-white/95 backdrop-blur-sm border border-oxford-blue/10 shadow-xl rounded-2xl">
            <div className="p-8 md:p-12">
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 text-4xl font-bold text-oxford-blue">"</div>
                <p className="mb-6 text-lg md:text-xl text-oxford-blue">{currentTestimonial!.content}</p>
                <Avatar className="h-16 w-16 mb-4">
                  <AvatarImage src={imageAsset.src} alt={imageAsset.alt} />
                  <AvatarFallback className="bg-starlight-yellow/20 text-oxford-blue">
                    {currentTestimonial!.author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="mt-4">
                  <p className="font-semibold text-oxford-blue">{currentTestimonial!.author}</p>
                  <p className="text-sm text-gray-600">{currentTestimonial!.role}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-center space-x-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full ${index === current ? "bg-oxford-blue" : "bg-oxford-blue/30"}`}
            onClick={() => setCurrent(index)}
          />
        ))}
      </div>

      <Button
        variant="outline"
        size="icon"
        className="absolute -left-4 top-1/2 -translate-y-1/2 rounded-full border-oxford-blue/20 bg-white/80 shadow-md hover:bg-white md:-left-6"
        onClick={prev}
      >
        <ChevronLeft className="h-5 w-5 text-oxford-blue" />
        <span className="sr-only">Previous testimonial</span>
      </Button>

      <Button
        variant="outline"
        size="icon"
        className="absolute -right-4 top-1/2 -translate-y-1/2 rounded-full border-oxford-blue/20 bg-white/80 shadow-md hover:bg-white md:-right-6"
        onClick={next}
      >
        <ChevronRight className="h-5 w-5 text-oxford-blue" />
        <span className="sr-only">Next testimonial</span>
      </Button>
    </div>
  )
}
