"use client"

import type React from "react"
import { useEffect, useState, useRef } from "react"
import { motion, useInView } from "framer-motion"

interface CountUpProps {
  end: number
  suffix?: string
  title: string
  description: string
  icon?: React.ReactNode
}

export default function CountUp({ end, suffix = "", title, description, icon }: CountUpProps) {
  const [count, setCount] = useState(0)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  useEffect(() => {
    if (isInView) {
      let startTime: number
      let animationFrame: number

      const countUp = (timestamp: number) => {
        if (!startTime) startTime = timestamp
        const progress = Math.min((timestamp - startTime) / 2000, 1)
        const currentCount = Math.floor(progress * end)

        setCount(currentCount)

        if (progress < 1) {
          animationFrame = requestAnimationFrame(countUp)
        }
      }

      animationFrame = requestAnimationFrame(countUp)

      return () => cancelAnimationFrame(animationFrame)
    }
  }, [end, isInView])

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5 }}
    >
      <div className="bg-white/10 backdrop-blur-sm border border-white/20 shadow-xl overflow-hidden rounded-2xl p-8 text-center text-white">
        {icon && <div className="mb-4 flex justify-center">{icon}</div>}
        <div className="text-4xl font-bold text-starlight-yellow">
          {count}
          {suffix}
        </div>
        <h3 className="mt-2 text-xl font-semibold">{title}</h3>
        <p className="mt-1 text-sm text-white/70">{description}</p>
      </div>
    </motion.div>
  )
}
