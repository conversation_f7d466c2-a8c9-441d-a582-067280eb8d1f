"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import { Brain, Heart, Home, LifeBuoy, School, Users, Briefcase } from "lucide-react"
import { useServices } from "@/hooks/use-data"

// Icon mapping for services
const iconMap = {
  LifeBuoy: LifeBuoy,
  Brain: Brain,
  Heart: Heart,
  Home: Home,
  School: School,
  Users: Users,
  Briefcase: Briefcase,
}

export default function ServicesSection() {
  const { data: services, loading, error } = useServices()
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: 0.2 })

  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const getServiceIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Home
    return <IconComponent className="h-10 w-10 text-starlight-yellow" />
  }

  // Show loading state
  if (loading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-gray-100 animate-pulse h-48 rounded-2xl"></div>
        ))}
      </div>
    )
  }

  // Show error state
  if (error || !services || services.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">
          {error ? `Error loading services: ${error}` : 'No services available'}
        </p>
      </div>
    )
  }

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "show" : "hidden"}
      className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
    >
      {services.map((service) => (
        <motion.div key={service.id} variants={itemVariants}>
          <div className="bg-white border border-gray-100 shadow-md h-full rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-starlight-yellow/30">
            <div className="mb-4">{getServiceIcon(service.icon || 'Home')}</div>
            <h3 className="text-xl font-bold text-oxford-blue">{service.title}</h3>
            <p className="mt-3 text-gray-600">{service.description}</p>
            {service.contactPerson && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">Contact: {service.contactPerson}</p>
                {service.phone && (
                  <p className="text-sm text-starlight-yellow font-medium">{service.phone}</p>
                )}
              </div>
            )}
          </div>
        </motion.div>
      ))}
    </motion.div>
  )
}
