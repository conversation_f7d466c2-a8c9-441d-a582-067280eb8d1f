"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import { Brain, Heart, Home, LifeBuoy, School, Users } from "lucide-react"

const services = [
  {
    icon: <LifeBuoy className="h-10 w-10 text-starlight-yellow" />,
    title: "Emergency Housing",
    description: "Immediate safe accommodation for those in crisis situations with 24/7 support.",
  },
  {
    icon: <Brain className="h-10 w-10 text-starlight-yellow" />,
    title: "Mental Health & Advocacy",
    description: "Professional support for mental wellbeing and personal advocacy services.",
  },
  {
    icon: <Heart className="h-10 w-10 text-starlight-yellow" />,
    title: "Benefits Support",
    description: "Assistance with navigating and accessing available benefits and financial aid.",
  },
  {
    icon: <Home className="h-10 w-10 text-starlight-yellow" />,
    title: "Resettlement Services",
    description: "Help with finding permanent housing and settling into communities safely.",
  },
  {
    icon: <School className="h-10 w-10 text-starlight-yellow" />,
    title: "Life Skills & Independence",
    description: "Training and support to build independence and confidence for the future.",
  },
  {
    icon: <Users className="h-10 w-10 text-starlight-yellow" />,
    title: "Community Integration",
    description: "Programs to help individuals connect with their local community and support networks.",
  },
]

export default function ServicesSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: 0.2 })

  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <motion.div
      ref={ref}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "show" : "hidden"}
      className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
    >
      {services.map((service, index) => (
        <motion.div key={index} variants={itemVariants}>
          <div className="bg-white border border-gray-100 shadow-md h-full rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:border-starlight-yellow/30">
            <div className="mb-4">{service.icon}</div>
            <h3 className="text-xl font-bold text-oxford-blue">{service.title}</h3>
            <p className="mt-3 text-gray-600">{service.description}</p>
          </div>
        </motion.div>
      ))}
    </motion.div>
  )
}
