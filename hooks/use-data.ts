import { useState, useEffect, useMemo } from 'react'
import { DataService } from '@/lib/data-service'
import type {
  Property,
  Service,
  Testimonial,
  TeamMember,
  WhoWeHelpCategory,
  Statistic,
  PropertyFilters,
  ServiceFilters
} from '@/types'

// Generic hook for data fetching with loading and error states
function useAsyncData<T>(
  fetchFunction: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true
    
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const result = await fetchFunction()
        if (isMounted) {
          setData(result)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'An error occurred')
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, dependencies)

  return { data, loading, error, refetch: () => fetchData() }
}

// Properties hooks
export function useProperties(filters?: PropertyFilters) {
  return useAsyncData(
    () => DataService.getProperties(filters),
    [JSON.stringify(filters)]
  )
}

export function useProperty(id: string) {
  return useAsyncData(
    () => DataService.getPropertyById(id),
    [id]
  )
}

// Services hooks
export function useServices(filters?: ServiceFilters) {
  return useAsyncData(
    () => DataService.getServices(filters),
    [JSON.stringify(filters)]
  )
}

export function useService(id: string) {
  return useAsyncData(
    () => DataService.getServiceById(id),
    [id]
  )
}

// Testimonials hooks
export function useTestimonials() {
  return useAsyncData(() => DataService.getTestimonials())
}

export function useTestimonial(id: string) {
  return useAsyncData(
    () => DataService.getTestimonialById(id),
    [id]
  )
}

// Team hooks
export function useTeamMembers() {
  return useAsyncData(() => DataService.getTeamMembers())
}

export function useTeamMember(id: string) {
  return useAsyncData(
    () => DataService.getTeamMemberById(id),
    [id]
  )
}

// Other content hooks
export function useWhoWeHelpCategories() {
  return useAsyncData(() => DataService.getWhoWeHelpCategories())
}

export function useStatistics() {
  return useAsyncData(() => DataService.getStatistics())
}

export function useSiteConfig() {
  return useAsyncData(() => DataService.getSiteConfig())
}

// Search hook
export function useSearch(query: string) {
  const [results, setResults] = useState<{
    properties: Property[]
    services: Service[]
    testimonials: Testimonial[]
    teamMembers: TeamMember[]
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!query.trim()) {
      setResults(null)
      return
    }

    let isMounted = true
    
    const performSearch = async () => {
      try {
        setLoading(true)
        setError(null)
        const searchResults = await DataService.searchContent(query)
        if (isMounted) {
          setResults(searchResults)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Search failed')
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    // Debounce search
    const timeoutId = setTimeout(performSearch, 300)

    return () => {
      clearTimeout(timeoutId)
      isMounted = false
    }
  }, [query])

  return { results, loading, error }
}

// Image asset hooks
export function useImageAsset(key: string, category: 'properties' | 'team' | 'testimonials' | 'general' = 'general') {
  return useMemo(() => {
    return {
      src: DataService.getImageUrl(key, category),
      alt: DataService.getImageAlt(key, category),
      asset: DataService.getImageAsset(key, category)
    }
  }, [key, category])
}

// Utility hook for filtering data
export function useFilteredData<T>(
  data: T[] | null,
  filterFunction: (item: T) => boolean
) {
  return useMemo(() => {
    if (!data) return null
    return data.filter(filterFunction)
  }, [data, filterFunction])
}
