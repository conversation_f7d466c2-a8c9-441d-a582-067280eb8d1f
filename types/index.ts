// Core data types for the Starlight Housing application

export interface Property {
  id: string
  title: string
  location: string
  bedrooms: number
  bathrooms: number
  rating: number
  image: string
  price: string
  features: string[]
  description: string
  residents?: string[]
  supportWorker?: string
  supportWorkerImage?: string
  isActive?: boolean
}

export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  contactPerson?: string
  phone?: string
  icon?: string
  isActive?: boolean
}

export interface Testimonial {
  id: string
  content: string
  author: string
  role: string
  image?: string
  isActive?: boolean
}

export interface TeamMember {
  id: string
  name: string
  role: string
  bio: string
  image?: string
  email?: string
  phone?: string
  isActive?: boolean
}

export interface NavigationItem {
  title: string
  href: string
  description?: string
  children?: NavigationItem[]
}

export interface ContactInfo {
  address: string
  city: string
  country: string
  phone: string
  email: string
  emergencyHelpline?: string
  supportPhone?: string
  supportEmail?: string
  housingEmail?: string
}

export interface SocialLinks {
  facebook?: string
  twitter?: string
  linkedin?: string
  instagram?: string
  youtube?: string
}

export interface SiteConfig {
  name: string
  description: string
  tagline?: string
  contact: ContactInfo
  social: SocialLinks
  features: {
    enableBooking?: boolean
    enableDonations?: boolean
    enableVolunteerSignup?: boolean
    enableNewsletter?: boolean
  }
}

export interface WhoWeHelpCategory {
  id: string
  title: string
  description: string
  icon?: string
  link?: string
  isActive?: boolean
}

export interface Statistic {
  id: string
  value: number
  suffix?: string
  title: string
  description: string
  icon?: string
}

export interface ThemeConfig {
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
  }
}

// Filter and query types
export interface PropertyFilters {
  location?: string
  bedrooms?: number
  priceRange?: string
  features?: string[]
}

export interface ServiceFilters {
  category?: string
  contactPerson?: string
}

// API response types
export interface DataResponse<T> {
  data: T
  success: boolean
  error?: string
}

// Image asset types
export interface ImageAsset {
  src: string
  alt: string
  width?: number
  height?: number
}

export interface AssetConfig {
  properties: Record<string, ImageAsset>
  team: Record<string, ImageAsset>
  testimonials: Record<string, ImageAsset>
  general: Record<string, ImageAsset>
}
