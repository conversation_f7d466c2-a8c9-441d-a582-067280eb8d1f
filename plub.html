<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Math Whiteboard</title>
  <script type="text/javascript" src="https://www.chenyuho.com/project/handwritingjs/handwriting.canvas.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      text-align: center;
      margin-bottom: 5px;
    }
    .subtitle {
      text-align: center;
      color: #7f8c8d;
      margin-bottom: 20px;
    }
    .whiteboard-container {
      position: relative;
      margin: 20px 0;
    }
    #canvas {
      background-color: white;
      border: 2px solid #3498db;
      border-radius: 5px;
      cursor: crosshair;
      display: block;
      margin: 0 auto;
      box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }
    .controls {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin: 20px 0;
      flex-wrap: wrap;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
    }
    button:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
    }
    button:active {
      transform: translateY(0);
    }
    button.secondary {
      background-color: #95a5a6;
    }
    button.secondary:hover {
      background-color: #7f8c8d;
    }
    button.danger {
      background-color: #e74c3c;
    }
    button.danger:hover {
      background-color: #c0392b;
    }
    .status {
      text-align: center;
      margin: 15px 0;
      font-weight: bold;
      min-height: 24px;
    }
    .result-container {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      margin-top: 20px;
      border-left: 5px solid #3498db;
    }
    .result-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #2c3e50;
    }
    #result {
      font-size: 24px;
      color: #27ae60;
    }
    .paint-mode {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      font-weight: bold;
    }
    .paint-off {
      background-color: #e74c3c;
      color: white;
    }
    .paint-on {
      background-color: #2ecc71;
      color: white;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    .modal-content {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 500px;
      width: 90%;
      text-align: center;
      box-shadow: 0 0 20px rgba(0,0,0,0.2);
    }
    .spinner {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .keyboard-shortcut {
      background-color: #ecf0f1;
      padding: 2px 5px;
      border-radius: 3px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- <h1>Interactive Math Whiteboard</h1>
    <p class="subtitle">Draw or write your equation/question, then click "Solve" to get the answer</p> -->
    
    <div class="whiteboard-container">
      <canvas id="canvas" width="800" height="400"></canvas>
    </div>
    
    <div class="status" id="status"></div>
    
    <div class="controls">
      <button onclick="recognizeAndSolve()">Solve</button>
      <button onclick="canvas.erase()" class="secondary">Clear Board</button>
      <button onclick="showHelp()" class="secondary">Help</button>
    </div>
    
    <div class="result-container">
      <div class="result-title">Solution:</div>
      <div id="result">Draw something and click "Solve"</div>
    </div>
    
    <div class="status">
      Paint mode: <span id='paintMode' class="paint-mode paint-off">OFF</span>
      <span style="margin-left: 20px;">Shortcut: Hold <span class="keyboard-shortcut">P</span> to paint</span>
    </div>
  </div>
  
  <!-- Loading Modal -->
  <div class="modal" id="loadingModal">
    <div class="modal-content">
      <div class="spinner"></div>
      <h3>Processing your request...</h3>
      <p id="loadingText">Recognizing handwriting and solving the problem</p>
    </div>
  </div>
  
  <!-- Help Modal -->
  <div class="modal" id="helpModal">
    <div class="modal-content">
      <h3>Whiteboard Help</h3>
      <p><strong>Drawing:</strong> Click and drag to draw on the whiteboard</p>
      <p><strong>Paint Mode:</strong> Hold <span class="keyboard-shortcut">P</span> key to enable continuous drawing without holding mouse button</p>
      <p><strong>Solve:</strong> Click the "Solve" button to recognize your handwriting and get the solution</p>
      <p><strong>Clear:</strong> Click "Clear Board" to erase everything</p>
      <p>This whiteboard can solve math equations, answer questions, and more!</p>
      <button onclick="hideHelp()" style="margin-top: 20px;">Got it!</button>
    </div>
  </div>

  <script>
    // Initialize handwriting canvas
    var canvas = new handwriting.Canvas(document.getElementById("canvas"), 3);
    canvas.setLineWidth(5);
    canvas.setOptions({
      language: "en",
      numOfReturn: 1
    });
    
    // Paint mode variables
    var paintMode = false;
    var x = 0, y = 0;
    
    // Track mouse position
    document.onmousemove = function(e) {
      x = e.clientX;
      y = e.clientY;
    };
    
    // Paint mode toggle with P key
    document.addEventListener("keydown", (event) => {
      if (event.code === "KeyP") {
        if (!paintMode) {
          paintMode = true;
          document.getElementById("paintMode").className = "paint-mode paint-on";
          document.getElementById("paintMode").innerHTML = "ON";
          sendMouseEvent();
        }
      }
    });
    
    document.addEventListener("keyup", (event) => {
      if (event.code === "KeyP") {
        if (paintMode) {
          paintMode = false;
          document.getElementById("paintMode").className = "paint-mode paint-off";
          document.getElementById("paintMode").innerHTML = "OFF";
          sendMouseEvent();
        }
      }
    });
    
    function sendMouseEvent() {
      var eventName = paintMode ? "mousedown" : "mouseup";
      var element = document.getElementById("canvas");
      var clickEvent = document.createEvent("MouseEvents");
      clickEvent.initMouseEvent(
        eventName,
        true,
        true,
        window,
        0,
        0,
        0,
        x,
        y,
        false,
        false,
        false,
        false,
        0,
        null
      );
      element.dispatchEvent(clickEvent);
    }
    
    // Modals control
    function showLoading(text) {
      if (text) document.getElementById("loadingText").innerText = text;
      document.getElementById("loadingModal").style.display = "flex";
    }
    
    function hideLoading() {
      document.getElementById("loadingModal").style.display = "none";
    }
    
    function showHelp() {
      document.getElementById("helpModal").style.display = "flex";
    }
    
    function hideHelp() {
      document.getElementById("helpModal").style.display = "none";
    }
    
    // Set callback for handwriting recognition
    canvas.setCallBack(function(data, err) {
      hideLoading();
      
      if (err) {
        document.getElementById("status").innerHTML = "Error: " + err.message;
        document.getElementById("result").innerHTML = "Recognition failed. Please try again.";
        return;
      }
      
      if (data && data.length > 0) {
        const recognizedText = data[0];
        document.getElementById("status").innerHTML = "Recognized: " + recognizedText;
        callGeminiAPI(recognizedText);
      } else {
        document.getElementById("status").innerHTML = "No text recognized. Please try again.";
        document.getElementById("result").innerHTML = "No input detected";
      }
    });
    
    // Main function to recognize and solve
    function recognizeAndSolve() {
      showLoading("Recognizing your handwriting...");
      document.getElementById("status").innerHTML = "Processing...";
      canvas.recognize();
    }
    
    // Call Gemini API
    const callGeminiAPI = async (query) => {
      showLoading("Solving the problem with Gemini AI...");
      
      try {
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyDeKkgEp9JC9nMvTquLIcj1n3X1mQr_9NA', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: `You are a smart calculator and question answering system. Your job is to provide ONLY the final answer, nothing else.  
                RULES: 
                - If it's a math problem, give only the numerical result 
                - If it's a question, give only the direct answer (maximum 5 words) 
                - No explanations, no steps, no extra text 
                - No "The answer is" or similar phrases 
                - Just the pure answer  
                
                Question/Problem: ${query}`
              }]
            }],
            generationConfig: {
              temperature: 0.1,
              topK: 1,
              topP: 0.1,
              maxOutputTokens: 50,
            }
          })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
          let answer = data.candidates[0].content.parts[0].text.trim();
          answer = answer.replace(/^(Answer:|Result:|Solution:)/i, '').trim();
          
          document.getElementById("result").innerHTML = answer;
          document.getElementById("status").innerHTML = "Solved successfully!";
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error) {
        console.error('Gemini API Error:', error);
        document.getElementById("result").innerHTML = "Error solving the problem. Please try again.";
        document.getElementById("status").innerHTML = "Error: " + error.message;
      } finally {
        hideLoading();
      }
    };
    
    // Fallback math solver (simple implementation)
    function solveMathFallback(query) {
      try {
        // Very basic fallback - only works for simple arithmetic
        const result = eval(query);
        return result.toString();
      } catch (e) {
        return "Could not solve this problem";
      }
    }
  </script>
</body>
</html>