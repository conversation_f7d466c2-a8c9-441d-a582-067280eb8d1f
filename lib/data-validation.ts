import type {
  Property,
  Service,
  Testimonial,
  TeamMember,
  WhoWeHelpCategory,
  Statistic,
  SiteConfig,
  ImageAsset
} from '@/types'

// Validation functions for data integrity
export class DataValidator {
  // Property validation
  static validateProperty(property: any): property is Property {
    const required = ['id', 'title', 'location', 'bedrooms', 'bathrooms', 'rating', 'image', 'price', 'features', 'description']
    
    for (const field of required) {
      if (!(field in property) || property[field] === undefined || property[field] === null) {
        console.warn(`Property validation failed: missing required field '${field}'`, property)
        return false
      }
    }
    
    // Type checks
    if (typeof property.bedrooms !== 'number' || property.bedrooms < 0) {
      console.warn('Property validation failed: bedrooms must be a positive number', property)
      return false
    }
    
    if (typeof property.bathrooms !== 'number' || property.bathrooms < 0) {
      console.warn('Property validation failed: bathrooms must be a positive number', property)
      return false
    }
    
    if (typeof property.rating !== 'number' || property.rating < 0 || property.rating > 5) {
      console.warn('Property validation failed: rating must be between 0 and 5', property)
      return false
    }
    
    if (!Array.isArray(property.features)) {
      console.warn('Property validation failed: features must be an array', property)
      return false
    }
    
    return true
  }
  
  // Service validation
  static validateService(service: any): service is Service {
    const required = ['id', 'title', 'description', 'features']
    
    for (const field of required) {
      if (!(field in service) || service[field] === undefined || service[field] === null) {
        console.warn(`Service validation failed: missing required field '${field}'`, service)
        return false
      }
    }
    
    if (!Array.isArray(service.features)) {
      console.warn('Service validation failed: features must be an array', service)
      return false
    }
    
    return true
  }
  
  // Testimonial validation
  static validateTestimonial(testimonial: any): testimonial is Testimonial {
    const required = ['id', 'content', 'author', 'role']
    
    for (const field of required) {
      if (!(field in testimonial) || testimonial[field] === undefined || testimonial[field] === null) {
        console.warn(`Testimonial validation failed: missing required field '${field}'`, testimonial)
        return false
      }
    }
    
    if (testimonial.content.length < 10) {
      console.warn('Testimonial validation failed: content too short', testimonial)
      return false
    }
    
    return true
  }
  
  // Team member validation
  static validateTeamMember(member: any): member is TeamMember {
    const required = ['id', 'name', 'role', 'bio']
    
    for (const field of required) {
      if (!(field in member) || member[field] === undefined || member[field] === null) {
        console.warn(`Team member validation failed: missing required field '${field}'`, member)
        return false
      }
    }
    
    return true
  }
  
  // Statistic validation
  static validateStatistic(statistic: any): statistic is Statistic {
    const required = ['id', 'value', 'title', 'description']
    
    for (const field of required) {
      if (!(field in statistic) || statistic[field] === undefined || statistic[field] === null) {
        console.warn(`Statistic validation failed: missing required field '${field}'`, statistic)
        return false
      }
    }
    
    if (typeof statistic.value !== 'number' || statistic.value < 0) {
      console.warn('Statistic validation failed: value must be a positive number', statistic)
      return false
    }
    
    return true
  }
  
  // Image asset validation
  static validateImageAsset(asset: any): asset is ImageAsset {
    const required = ['src', 'alt']
    
    for (const field of required) {
      if (!(field in asset) || asset[field] === undefined || asset[field] === null) {
        console.warn(`Image asset validation failed: missing required field '${field}'`, asset)
        return false
      }
    }
    
    // Check if src is a valid path
    if (!asset.src.startsWith('/') && !asset.src.startsWith('http')) {
      console.warn('Image asset validation failed: src must be a valid path or URL', asset)
      return false
    }
    
    return true
  }
  
  // Validate entire configuration
  static validateConfiguration() {
    const errors: string[] = []
    
    try {
      // Import and validate all configurations
      const { properties } = require('@/config/properties')
      const { services } = require('@/config/services')
      const { testimonials } = require('@/config/testimonials')
      const { teamMembers } = require('@/config/team')
      const { statistics } = require('@/config/statistics')
      const { assetConfig } = require('@/config/assets')
      
      // Validate properties
      properties.forEach((property: any, index: number) => {
        if (!this.validateProperty(property)) {
          errors.push(`Invalid property at index ${index}: ${property.id || 'unknown'}`)
        }
      })
      
      // Validate services
      services.forEach((service: any, index: number) => {
        if (!this.validateService(service)) {
          errors.push(`Invalid service at index ${index}: ${service.id || 'unknown'}`)
        }
      })
      
      // Validate testimonials
      testimonials.forEach((testimonial: any, index: number) => {
        if (!this.validateTestimonial(testimonial)) {
          errors.push(`Invalid testimonial at index ${index}: ${testimonial.id || 'unknown'}`)
        }
      })
      
      // Validate team members
      teamMembers.forEach((member: any, index: number) => {
        if (!this.validateTeamMember(member)) {
          errors.push(`Invalid team member at index ${index}: ${member.id || 'unknown'}`)
        }
      })
      
      // Validate statistics
      statistics.forEach((statistic: any, index: number) => {
        if (!this.validateStatistic(statistic)) {
          errors.push(`Invalid statistic at index ${index}: ${statistic.id || 'unknown'}`)
        }
      })
      
      // Validate image assets
      Object.entries(assetConfig).forEach(([category, assets]) => {
        Object.entries(assets as Record<string, any>).forEach(([key, asset]) => {
          if (!this.validateImageAsset(asset)) {
            errors.push(`Invalid image asset in ${category}: ${key}`)
          }
        })
      })
      
    } catch (error) {
      errors.push(`Configuration validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
    
    if (errors.length > 0) {
      console.error('Configuration validation errors:', errors)
      return { valid: false, errors }
    }
    
    console.log('Configuration validation passed')
    return { valid: true, errors: [] }
  }
}

// Error handling utilities
export class DataErrorHandler {
  static handleMissingData(type: string, id?: string) {
    const message = id 
      ? `${type} with id '${id}' not found`
      : `No ${type} data available`
    
    console.warn(message)
    return null
  }
  
  static handleValidationError(type: string, data: any, error: string) {
    console.error(`${type} validation error:`, error, data)
    return null
  }
  
  static handleAssetError(key: string, category: string) {
    console.warn(`Asset '${key}' not found in category '${category}', using fallback`)
    return {
      src: '/placeholder.svg',
      alt: 'Placeholder image',
      width: 400,
      height: 300
    }
  }
}

// Initialize validation on import (development only)
if (process.env.NODE_ENV === 'development') {
  // Run validation after a short delay to ensure all modules are loaded
  setTimeout(() => {
    DataValidator.validateConfiguration()
  }, 1000)
}
