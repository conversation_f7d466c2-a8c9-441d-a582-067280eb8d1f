import {
  properties,
  services,
  testimonials,
  teamMembers,
  whoWeHelpCategories,
  statistics,
  siteConfig,
  assetConfig
} from '@/config'
import type {
  Property,
  Service,
  Testimonial,
  TeamMember,
  WhoWeHelpCategory,
  Statistic,
  PropertyFilters,
  ServiceFilters,
  DataResponse,
  ImageAsset
} from '@/types'

/**
 * Data service for managing local configuration-based data
 * This service provides a consistent API for accessing all application data
 */
export class DataService {
  // Properties
  static async getProperties(filters?: PropertyFilters): Promise<Property[]> {
    let filteredProperties = properties.filter(property => property.isActive !== false)
    
    if (filters) {
      if (filters.location) {
        filteredProperties = filteredProperties.filter(property =>
          property.location.toLowerCase().includes(filters.location!.toLowerCase())
        )
      }
      
      if (filters.bedrooms) {
        filteredProperties = filteredProperties.filter(property =>
          property.bedrooms === filters.bedrooms
        )
      }
      
      if (filters.features && filters.features.length > 0) {
        filteredProperties = filteredProperties.filter(property =>
          filters.features!.some(feature =>
            property.features.some(propFeature =>
              propFeature.toLowerCase().includes(feature.toLowerCase())
            )
          )
        )
      }
    }
    
    return filteredProperties
  }
  
  static async getPropertyById(id: string): Promise<Property | null> {
    const property = properties.find(p => p.id === id && p.isActive !== false)
    return property || null
  }
  
  // Services
  static async getServices(filters?: ServiceFilters): Promise<Service[]> {
    let filteredServices = services.filter(service => service.isActive !== false)
    
    if (filters) {
      if (filters.contactPerson) {
        filteredServices = filteredServices.filter(service =>
          service.contactPerson?.toLowerCase().includes(filters.contactPerson!.toLowerCase())
        )
      }
    }
    
    return filteredServices
  }
  
  static async getServiceById(id: string): Promise<Service | null> {
    const service = services.find(s => s.id === id && s.isActive !== false)
    return service || null
  }
  
  // Testimonials
  static async getTestimonials(): Promise<Testimonial[]> {
    return testimonials.filter(testimonial => testimonial.isActive !== false)
  }
  
  static async getTestimonialById(id: string): Promise<Testimonial | null> {
    const testimonial = testimonials.find(t => t.id === id && t.isActive !== false)
    return testimonial || null
  }
  
  // Team Members
  static async getTeamMembers(): Promise<TeamMember[]> {
    return teamMembers.filter(member => member.isActive !== false)
  }
  
  static async getTeamMemberById(id: string): Promise<TeamMember | null> {
    const member = teamMembers.find(m => m.id === id && m.isActive !== false)
    return member || null
  }
  
  // Who We Help Categories
  static async getWhoWeHelpCategories(): Promise<WhoWeHelpCategory[]> {
    return whoWeHelpCategories.filter(category => category.isActive !== false)
  }
  
  // Statistics
  static async getStatistics(): Promise<Statistic[]> {
    return statistics
  }
  
  // Site Configuration
  static async getSiteConfig() {
    return siteConfig
  }
  
  // Asset Management
  static getImageAsset(key: string, category: keyof typeof assetConfig = 'general'): ImageAsset | null {
    const categoryAssets = assetConfig[category]
    return categoryAssets[key] || null
  }
  
  static getImageUrl(key: string, category: keyof typeof assetConfig = 'general'): string {
    const asset = this.getImageAsset(key, category)
    return asset?.src || '/placeholder.svg'
  }
  
  static getImageAlt(key: string, category: keyof typeof assetConfig = 'general'): string {
    const asset = this.getImageAsset(key, category)
    return asset?.alt || 'Image'
  }
  
  // Search functionality
  static async searchContent(query: string): Promise<{
    properties: Property[]
    services: Service[]
    testimonials: Testimonial[]
    teamMembers: TeamMember[]
  }> {
    const searchTerm = query.toLowerCase()
    
    const searchProperties = properties.filter(property =>
      property.isActive !== false && (
        property.title.toLowerCase().includes(searchTerm) ||
        property.location.toLowerCase().includes(searchTerm) ||
        property.description.toLowerCase().includes(searchTerm) ||
        property.features.some(feature => feature.toLowerCase().includes(searchTerm))
      )
    )
    
    const searchServices = services.filter(service =>
      service.isActive !== false && (
        service.title.toLowerCase().includes(searchTerm) ||
        service.description.toLowerCase().includes(searchTerm) ||
        service.features.some(feature => feature.toLowerCase().includes(searchTerm))
      )
    )
    
    const searchTestimonials = testimonials.filter(testimonial =>
      testimonial.isActive !== false && (
        testimonial.content.toLowerCase().includes(searchTerm) ||
        testimonial.author.toLowerCase().includes(searchTerm) ||
        testimonial.role.toLowerCase().includes(searchTerm)
      )
    )
    
    const searchTeamMembers = teamMembers.filter(member =>
      member.isActive !== false && (
        member.name.toLowerCase().includes(searchTerm) ||
        member.role.toLowerCase().includes(searchTerm) ||
        member.bio.toLowerCase().includes(searchTerm)
      )
    )
    
    return {
      properties: searchProperties,
      services: searchServices,
      testimonials: searchTestimonials,
      teamMembers: searchTeamMembers
    }
  }
}
