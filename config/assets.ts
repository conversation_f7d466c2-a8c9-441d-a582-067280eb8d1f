import { AssetConfig } from '@/types'

export const assetConfig: AssetConfig = {
  properties: {
    riverside_sanctuary: {
      src: "/images/st1.png",
      alt: "Riverside Sanctuary - Modern riverside accommodation",
      width: 400,
      height: 300
    },
    city_center_haven: {
      src: "/images/st2.png", 
      alt: "City Center Haven - Urban apartment with excellent transport links",
      width: 400,
      height: 300
    },
    green_meadows: {
      src: "/images/st3.png",
      alt: "Green Meadows - Peaceful suburban home with garden space",
      width: 400,
      height: 300
    },
    community_house: {
      src: "/images/st6.png",
      alt: "Community House - Shared living environment with peer support",
      width: 400,
      height: 300
    },
    urban_oasis: {
      src: "/images/st8.png",
      alt: "Urban Oasis - Modern apartment with employment opportunities",
      width: 400,
      height: 300
    },
    starlight_home: {
      src: "/images/sthome.png",
      alt: "Starlight Home - Comfortable accommodation with full support",
      width: 400,
      height: 300
    }
  },
  team: {
    sarah_johnson: {
      src: "/placeholder-user.jpg",
      alt: "<PERSON> - Executive Director",
      width: 300,
      height: 300
    },
    michael_chen: {
      src: "/placeholder-user.jpg", 
      alt: "<PERSON> - Housing Director",
      width: 300,
      height: 300
    },
    amina_patel: {
      src: "/placeholder-user.jpg",
      alt: "Amina Patel - Support Services Manager", 
      width: 300,
      height: 300
    },
    emma_thompson: {
      src: "/placeholder-user.jpg",
      alt: "Emma Thompson - Support Worker",
      width: 200,
      height: 200
    },
    david_wilson: {
      src: "/placeholder-user.jpg",
      alt: "David Wilson - Support Worker",
      width: 200,
      height: 200
    },
    rachel_green: {
      src: "/placeholder-user.jpg",
      alt: "Rachel Green - Support Worker",
      width: 200,
      height: 200
    },
    ian_macleod: {
      src: "/placeholder-user.jpg",
      alt: "Ian MacLeod - Support Worker",
      width: 200,
      height: 200
    }
  },
  testimonials: {
    sarah_m: {
      src: "/placeholder-user.jpg",
      alt: "Sarah M. - Housing Support Recipient",
      width: 100,
      height: 100
    },
    michael_r: {
      src: "/placeholder-user.jpg",
      alt: "Michael R. - Community Resident", 
      width: 100,
      height: 100
    },
    james_k: {
      src: "/placeholder-user.jpg",
      alt: "James K. - Program Graduate",
      width: 100,
      height: 100
    },
    lisa_t: {
      src: "/placeholder-user.jpg",
      alt: "Lisa T. - Support Services User",
      width: 100,
      height: 100
    }
  },
  general: {
    hero_background: {
      src: "/images/vision-bg.png",
      alt: "Starlight Housing - A Place to Call Home",
      width: 1920,
      height: 1080
    },
    placeholder: {
      src: "/placeholder.svg",
      alt: "Placeholder image",
      width: 400,
      height: 300
    },
    logo: {
      src: "/placeholder-logo.svg",
      alt: "Starlight Housing Logo",
      width: 200,
      height: 60
    }
  }
}
