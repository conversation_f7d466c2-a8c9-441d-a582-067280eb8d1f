import { TeamMember } from '@/types'

export const teamMembers: Team<PERSON>ember[] = [
  {
    id: "sarah-johnson",
    name: "<PERSON>",
    role: "Executive Director",
    bio: "With over 15 years in social services, <PERSON> leads our organization with compassion and vision.",
    image: "sarah_johnson",
    email: "<EMAIL>",
    phone: "+44 20 1234 5678",
    isActive: true
  },
  {
    id: "michael-chen",
    name: "<PERSON>",
    role: "Housing Director",
    bio: "<PERSON> oversees our property portfolio and ensures all our homes meet the highest standards.",
    image: "michael_chen",
    email: "<EMAIL>",
    phone: "+44 ************",
    isActive: true
  },
  {
    id: "amina-patel",
    name: "<PERSON><PERSON>",
    role: "Support Services Manager",
    bio: "<PERSON><PERSON> coordinates our comprehensive support services, focusing on resident wellbeing.",
    image: "amina_patel",
    email: "<EMAIL>",
    phone: "+44 ************",
    isActive: true
  },
  {
    id: "emma-thompson",
    name: "<PERSON>",
    role: "Senior Support Worker",
    bio: "<PERSON> provides direct support to residents, specializing in crisis intervention and mental health support.",
    image: "emma_thompson",
    email: "<EMAIL>",
    isActive: true
  },
  {
    id: "david-wilson",
    name: "<PERSON>",
    role: "Support Worker",
    bio: "David works closely with residents to develop independent living skills and community connections.",
    image: "david_wilson",
    email: "<EMAIL>",
    isActive: true
  },
  {
    id: "rachel-green",
    name: "Rachel Green",
    role: "Support Worker",
    bio: "Rachel specializes in employment support and life skills training for residents.",
    image: "rachel_green",
    email: "<EMAIL>",
    isActive: true
  },
  {
    id: "ian-macleod",
    name: "Ian MacLeod",
    role: "Community Support Coordinator",
    bio: "Ian manages our shared living programs and peer support initiatives.",
    image: "ian_macleod",
    email: "<EMAIL>",
    isActive: true
  }
]
