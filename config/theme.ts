import { ThemeConfig } from '@/types'

export const themes: Record<string, ThemeConfig> = {
  starlight: {
    name: "Starlight",
    colors: {
      primary: "248 214 19", // Starlight Yellow #F8D613
      secondary: "15 22 50", // Oxford Blue #0F1632
      accent: "0 159 253",   // Carolina Blue #009FFD
      background: "255 249 253", // Snow White #FFF9FD
      foreground: "15 22 50"  // Oxford Blue #0F1632
    }
  },
  professional: {
    name: "Professional",
    colors: {
      primary: "59 130 246", // Blue
      secondary: "15 23 42", // Slate
      accent: "34 197 94",   // Green
      background: "255 255 255", // White
      foreground: "15 23 42"  // Slate
    }
  },
  warm: {
    name: "Warm",
    colors: {
      primary: "251 146 60", // Orange
      secondary: "124 58 237", // Purple
      accent: "236 72 153",   // Pink
      background: "254 252 232", // Warm white
      foreground: "124 58 237"  // Purple
    }
  }
}

export const defaultTheme = 'starlight'

// CSS custom properties mapping
export const cssVariableMapping = {
  primary: '--primary',
  secondary: '--secondary', 
  accent: '--accent',
  background: '--background',
  foreground: '--foreground',
  // Additional mappings for consistency
  'primary-foreground': '--primary-foreground',
  'secondary-foreground': '--secondary-foreground',
  'accent-foreground': '--accent-foreground',
  card: '--card',
  'card-foreground': '--card-foreground',
  popover: '--popover',
  'popover-foreground': '--popover-foreground',
  muted: '--muted',
  'muted-foreground': '--muted-foreground',
  destructive: '--destructive',
  'destructive-foreground': '--destructive-foreground',
  border: '--border',
  input: '--input',
  ring: '--ring'
}

// Function to apply theme
export function applyTheme(themeName: string = defaultTheme) {
  if (typeof window === 'undefined') return
  
  const theme = themes[themeName]
  if (!theme) {
    console.warn(`Theme '${themeName}' not found, using default theme`)
    return applyTheme(defaultTheme)
  }
  
  const root = document.documentElement
  
  // Apply theme colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    const cssVar = cssVariableMapping[key as keyof typeof cssVariableMapping]
    if (cssVar) {
      root.style.setProperty(cssVar, value)
    }
  })
  
  // Store current theme
  localStorage.setItem('starlight-theme', themeName)
}

// Function to get current theme
export function getCurrentTheme(): string {
  if (typeof window === 'undefined') return defaultTheme
  return localStorage.getItem('starlight-theme') || defaultTheme
}

// Function to get available themes
export function getAvailableThemes(): ThemeConfig[] {
  return Object.values(themes)
}
