import { Property } from '@/types'

export const properties: Property[] = [
  {
    id: "riverside-sanctuary",
    title: "Riverside Sanctuary",
    location: "Manchester, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.9,
    image: "riverside_sanctuary", // References assetConfig key
    price: "Available",
    features: ["Fully Furnished", "Support Services", "Community Garden"],
    description: "A peaceful riverside home with modern amenities and 24/7 support services.",
    residents: ["<PERSON>, 28", "<PERSON>, 34"],
    supportWorker: "<PERSON> Thompson",
    supportWorkerImage: "emma_thompson",
    isActive: true
  },
  {
    id: "city-center-haven",
    title: "City Center Haven",
    location: "Birmingham, UK",
    bedrooms: 1,
    bathrooms: 1,
    rating: 4.8,
    image: "city_center_haven",
    price: "Available",
    features: ["Transport Links", "Medical Support", "Shared Spaces"],
    description: "Modern studio apartment in the heart of the city with excellent transport connections.",
    residents: ["James, 25"],
    supportWorker: "<PERSON>",
    supportWorkerImage: "david_wilson",
    isActive: true
  },
  {
    id: "green-meadows",
    title: "Green Meadows",
    location: "Leeds, UK",
    bedrooms: 3,
    bathrooms: 2,
    rating: 4.7,
    image: "green_meadows",
    price: "Available",
    features: ["Garden Space", "Pet Friendly", "Quiet Area"],
    description: "Spacious family home in a quiet residential area with beautiful garden space.",
    residents: ["Family of 4"],
    supportWorker: "Rachel Green",
    supportWorkerImage: "rachel_green",
    isActive: true
  },
  {
    id: "urban-oasis",
    title: "Urban Oasis",
    location: "London, UK",
    bedrooms: 1,
    bathrooms: 1,
    rating: 4.6,
    image: "urban_oasis",
    price: "Available",
    features: ["Central Location", "Job Support", "Life Skills Training"],
    description: "Modern apartment with excellent access to employment opportunities and training.",
    residents: ["Carlos, 29"],
    supportWorker: "Rachel Green",
    supportWorkerImage: "rachel_green",
    isActive: true
  },
  {
    id: "community-house",
    title: "Community House",
    location: "Glasgow, UK",
    bedrooms: 4,
    bathrooms: 2,
    rating: 4.8,
    image: "community_house",
    price: "Available",
    features: ["Shared Living", "Peer Support", "Skills Development"],
    description: "Supportive shared living environment with focus on community and personal growth.",
    residents: ["Multiple residents", "Peer mentors"],
    supportWorker: "Ian MacLeod",
    supportWorkerImage: "ian_macleod",
    isActive: true
  },
  {
    id: "starlight-home",
    title: "Starlight Home",
    location: "Bristol, UK",
    bedrooms: 2,
    bathrooms: 1,
    rating: 4.9,
    image: "starlight_home",
    price: "Available",
    features: ["24/7 Support", "Therapy Access", "Recovery Programs"],
    description: "Specialized accommodation for individuals in recovery with comprehensive support services.",
    residents: ["Recovery residents"],
    supportWorker: "Emma Thompson",
    supportWorkerImage: "emma_thompson",
    isActive: true
  }
]
