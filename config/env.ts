// Environment-based configuration
export const env = {
  // Feature flags
  features: {
    enableBooking: process.env.NEXT_PUBLIC_ENABLE_BOOKING === 'true' || true,
    enableDonations: process.env.NEXT_PUBLIC_ENABLE_DONATIONS === 'true' || true,
    enableVolunteerSignup: process.env.NEXT_PUBLIC_ENABLE_VOLUNTEER === 'true' || true,
    enableNewsletter: process.env.NEXT_PUBLIC_ENABLE_NEWSLETTER === 'true' || true,
    enableLiveChat: process.env.NEXT_PUBLIC_ENABLE_LIVE_CHAT === 'true' || false,
    enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true' || false,
    enableMaintenanceMode: process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true' || false
  },
  
  // API Configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '5000'),
    retries: parseInt(process.env.NEXT_PUBLIC_API_RETRIES || '3')
  },
  
  // External Services
  services: {
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
    emailServiceUrl: process.env.NEXT_PUBLIC_EMAIL_SERVICE_URL || '',
    analyticsId: process.env.NEXT_PUBLIC_ANALYTICS_ID || '',
    chatbotId: process.env.NEXT_PUBLIC_CHATBOT_ID || ''
  },
  
  // Development settings
  development: {
    enableDebugMode: process.env.NODE_ENV === 'development',
    showTestData: process.env.NEXT_PUBLIC_SHOW_TEST_DATA === 'true' || false,
    enableMockApi: process.env.NEXT_PUBLIC_MOCK_API === 'true' || false
  },
  
  // Content settings
  content: {
    maxPropertiesPerPage: parseInt(process.env.NEXT_PUBLIC_MAX_PROPERTIES_PER_PAGE || '12'),
    maxTestimonialsPerSlide: parseInt(process.env.NEXT_PUBLIC_MAX_TESTIMONIALS_PER_SLIDE || '3'),
    enableContentFiltering: process.env.NEXT_PUBLIC_ENABLE_CONTENT_FILTERING === 'true' || true,
    defaultLanguage: process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || 'en'
  },
  
  // Performance settings
  performance: {
    enableImageOptimization: process.env.NEXT_PUBLIC_ENABLE_IMAGE_OPTIMIZATION !== 'false',
    enableCaching: process.env.NEXT_PUBLIC_ENABLE_CACHING !== 'false',
    cacheTimeout: parseInt(process.env.NEXT_PUBLIC_CACHE_TIMEOUT || '300000') // 5 minutes
  }
}

// Validation function to check required environment variables
export function validateEnvironment() {
  const errors: string[] = []
  
  // Add validation for required environment variables
  if (env.features.enableAnalytics && !env.services.analyticsId) {
    errors.push('Analytics is enabled but NEXT_PUBLIC_ANALYTICS_ID is not set')
  }
  
  if (env.features.enableLiveChat && !env.services.chatbotId) {
    errors.push('Live chat is enabled but NEXT_PUBLIC_CHATBOT_ID is not set')
  }
  
  if (errors.length > 0) {
    console.warn('Environment validation warnings:', errors)
  }
  
  return errors.length === 0
}

// Initialize environment validation
if (typeof window === 'undefined') {
  // Only run on server side
  validateEnvironment()
}
