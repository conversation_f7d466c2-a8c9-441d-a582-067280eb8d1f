import { Statistic } from '@/types'

export const statistics: Statistic[] = [
  {
    id: "people-housed",
    value: 2500,
    suffix: "+",
    title: "People Housed",
    description: "Individuals provided with safe accommodation",
    icon: "Home"
  },
  {
    id: "properties-managed",
    value: 150,
    suffix: "+",
    title: "Properties",
    description: "Homes across the UK providing support",
    icon: "Building"
  },
  {
    id: "support-hours",
    value: 50000,
    suffix: "+",
    title: "Support Hours",
    description: "Hours of dedicated support provided annually",
    icon: "Clock"
  },
  {
    id: "success-rate",
    value: 85,
    suffix: "%",
    title: "Success Rate",
    description: "Residents achieving independent living",
    icon: "TrendingUp"
  },
  {
    id: "years-experience",
    value: 14,
    suffix: "",
    title: "Years Experience",
    description: "Providing housing solutions since 2010",
    icon: "Calendar"
  },
  {
    id: "staff-members",
    value: 75,
    suffix: "+",
    title: "Staff Members",
    description: "Dedicated professionals supporting residents",
    icon: "Users"
  }
]
