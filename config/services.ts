import { Service } from '@/types'

export const services: Service[] = [
  {
    id: "emergency-housing",
    title: "Emergency Housing",
    description: "Immediate safe accommodation for those in crisis situations with 24/7 support.",
    features: ["24/7 availability", "Crisis intervention", "Immediate placement", "Safety assessment"],
    contactPerson: "Emergency Team",
    phone: "0800 123 4567",
    icon: "LifeBuoy",
    isActive: true
  },
  {
    id: "mental-health-advocacy",
    title: "Mental Health & Advocacy",
    description: "Professional support for mental wellbeing and personal advocacy services.",
    features: ["Counseling services", "Therapy sessions", "Advocacy support", "Peer mentoring"],
    contactPerson: "Dr. <PERSON>",
    phone: "0161 234 5678",
    icon: "Brain",
    isActive: true
  },
  {
    id: "housing-support",
    title: "Housing Support & Maintenance",
    description: "Comprehensive housing support including property maintenance and tenancy guidance.",
    features: ["Property maintenance", "Tenancy support", "Housing advice", "Rent assistance"],
    contactPerson: "<PERSON>",
    phone: "0161 234 5680",
    icon: "Home",
    isActive: true
  },
  {
    id: "life-skills-independence",
    title: "Life Skills & Independence",
    description: "Training and support to build independence and confidence for the future.",
    features: ["Cooking classes", "Budgeting workshops", "Job readiness", "Digital literacy"],
    contactPerson: "James <PERSON>",
    phone: "0207 567 8901",
    icon: "School",
    isActive: true
  },
  {
    id: "community-integration",
    title: "Community Integration",
    description: "Programs to help individuals connect with their local community and support networks.",
    features: ["Social activities", "Volunteer opportunities", "Support groups", "Community events"],
    contactPerson: "Lisa Patel",
    phone: "0141 678 9012",
    icon: "Users",
    isActive: true
  },
  {
    id: "employment-training",
    title: "Employment & Training",
    description: "Job placement assistance and skills training to help residents achieve financial independence.",
    features: ["CV writing", "Interview preparation", "Skills training", "Job placement"],
    contactPerson: "David Martinez",
    phone: "0207 567 8902",
    icon: "Briefcase",
    isActive: true
  }
]
