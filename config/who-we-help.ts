import { WhoWeHelpCategory } from '@/types'

export const whoWeHelpCategories: WhoWeHelpCategory[] = [
  {
    id: "homeless-individuals",
    title: "Homeless Individuals",
    description: "Providing safe accommodation and support for those experiencing homelessness.",
    icon: "Home",
    link: "/who-we-help#homeless",
    isActive: true
  },
  {
    id: "refugees-asylum-seekers",
    title: "Refugees & Asylum Seekers",
    description: "Supporting displaced individuals with housing and integration services.",
    icon: "UserRound",
    link: "/who-we-help#refugees",
    isActive: true
  },
  {
    id: "youth-in-crisis",
    title: "Youth in Crisis",
    description: "Specialized support for young people facing housing insecurity.",
    icon: "Users",
    link: "/who-we-help#youth",
    isActive: true
  },
  {
    id: "local-authority-referrals",
    title: "Local Authority Referrals",
    description: "Working with councils to provide housing solutions for vulnerable residents.",
    icon: "Building",
    link: "/who-we-help#local-authority",
    isActive: true
  },
  {
    id: "domestic-violence-survivors",
    title: "Domestic Violence Survivors",
    description: "Safe housing and specialized support for survivors of domestic violence.",
    icon: "Shield",
    link: "/who-we-help#domestic-violence",
    isActive: true
  },
  {
    id: "mental-health-support",
    title: "Mental Health Support Needs",
    description: "Accommodation and care for individuals with mental health challenges.",
    icon: "Heart",
    link: "/who-we-help#mental-health",
    isActive: true
  }
]
