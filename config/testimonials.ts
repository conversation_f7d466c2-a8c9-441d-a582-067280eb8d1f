import { Testimonial } from '@/types'

export const testimonials: Testimonial[] = [
  {
    id: "sarah-m",
    content: "<PERSON><PERSON> helped me find peace and safety when I had nowhere else to turn. Their support changed my life.",
    author: "<PERSON>",
    role: "Housing Support Recipient",
    image: "sarah_m",
    isActive: true
  },
  {
    id: "michael-r",
    content: "Their team made us feel like family. The comprehensive support they provide goes beyond just housing.",
    author: "<PERSON>",
    role: "Community Resident",
    image: "michael_r",
    isActive: true
  },
  {
    id: "james-k",
    content: "The life skills training gave me confidence to live independently. I now have my own place and a steady job.",
    author: "<PERSON>",
    role: "Program Graduate",
    image: "james_k",
    isActive: true
  },
  {
    id: "lisa-t",
    content: "The mental health support was exactly what I needed. The counselors are compassionate and professional.",
    author: "<PERSON>",
    role: "Support Services User",
    image: "lisa_t",
    isActive: true
  },
  {
    id: "carlos-m",
    content: "From emergency housing to permanent accommodation, <PERSON><PERSON> was with me every step of the way.",
    author: "<PERSON>",
    role: "Resident",
    image: "sarah_m", // Reusing image for privacy
    isActive: true
  },
  {
    id: "emma-w",
    content: "The community integration programs helped me build lasting friendships and feel part of something bigger.",
    author: "Emma W.",
    role: "Community Member",
    image: "michael_r", // Reusing image for privacy
    isActive: true
  }
]
