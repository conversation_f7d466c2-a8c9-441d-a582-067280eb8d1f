import { NavigationItem } from '@/types'

export const mainNavigation: NavigationItem[] = [
  {
    title: "Home",
    href: "/"
  },
  {
    title: "About",
    href: "/about"
  },
  {
    title: "Services",
    href: "/services",
    children: [
      {
        title: "Emergency Housing",
        href: "/services/emergency",
        description: "24/7 crisis support and immediate accommodation"
      },
      {
        title: "Mental Health & Advocacy",
        href: "/services/mental-health",
        description: "Professional counseling and advocacy services"
      },
      {
        title: "Life Skills & Independence",
        href: "/services/life-skills",
        description: "Training and support for independent living"
      },
      {
        title: "Community Integration",
        href: "/services/community",
        description: "Programs to connect with local community"
      }
    ]
  },
  {
    title: "Properties",
    href: "/properties"
  },
  {
    title: "Get Involved",
    href: "/get-involved"
  },
  {
    title: "Contact",
    href: "/contact"
  }
]

export const footerNavigation = {
  quickLinks: [
    { title: "Home", href: "/" },
    { title: "About", href: "/about" },
    { title: "Properties", href: "/properties" },
    { title: "Services", href: "/services" }
  ],
  getInvolved: [
    { title: "Volunteer", href: "/get-involved#volunteer" },
    { title: "Donate", href: "/get-involved#donate" },
    { title: "Partner With Us", href: "/get-involved#partner" }
  ],
  services: [
    { title: "Emergency Housing", href: "/services/emergency" },
    { title: "Mental Health Support", href: "/services/mental-health" },
    { title: "Life Skills Training", href: "/services/life-skills" },
    { title: "Community Programs", href: "/services/community" }
  ]
}

export const mobileNavigation: NavigationItem[] = [
  { title: "Home", href: "/" },
  { title: "About", href: "/about" },
  { title: "Services", href: "/services" },
  { title: "Properties", href: "/properties" },
  { title: "Get Involved", href: "/get-involved" },
  { title: "Contact", href: "/contact" }
]
